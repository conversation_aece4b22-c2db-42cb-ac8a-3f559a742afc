from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter, defaultdict
from datetime import datetime
from json import dumps
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter, mktime

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    SnortConfig,
    SnortParser,
    MYSQLConfig,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    hms_to_hourkey,
    is_private,
    separator,
)

from base.models import (
    # PublicIP,
    Sensor,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Hourly Parse Snort'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        today_ymd = get_today_ymd()

        sensor_list_of_names = Sensor.get_list_of_names()

        if not sensor_list_of_names:
            return abort(self, 'no sensors.')

        for sensor_name in sensor_list_of_names:
            sensor_start = perf_counter()

            dest_dir          = f'{SnortConfig.get_logs_parsed_dir()}/{sensor_name}/{today_ymd}'
            # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
            log_file          = f'{dest_dir}/{today_ymd}.log'

            database_name = create_name_of_database(SnortConfig.SLUG.value, today_ymd, sensor_name)
            instance      = SnortParser(slug=SnortConfig.SLUG.value, ymd=today_ymd, object_name=sensor_name)

            ## get instance.rows
            ## (throws exception if database does not exist)
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute(f'{SnortConfig.get_select_statement()};')
                        instance.rows = cur.fetchall()
            except:
                pass

            ## instance.rows = [
            ##     (1, '2024-08-29', '09:47:29', ...),
            ##     (2, '2024-08-29', '09:47:30', ...),
            ##     ...
            ## ]

            ## instance.rows can be empty
            ## even if reading database was successful
            if not instance.rows:
                ## NOTE do NOT abort(...)
                ##      otherwise other instances will not be parsed.
                ##      use print(...) and continue instead
                print(colorize(self, 'error', f'empty rows from database {database_name}'))
                continue


            ## needed for creating log_file
            if not path.exists(dest_dir):
                print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                makedirs(dest_dir)



            ## __INDEXES_ONE_OFF__

            save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

            for hms, count in Counter(map(itemgetter(1+1), instance.rows)).items():
                ## {'00:49:51': 12, '02:59:55': 1182, ...}
                ## ->
                ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                instance.times_and_counts[hms_to_hourkey(hms)] += count

                ## -----

                ## today_ymd hms -> millisecond
                ## (2023-05-12 00:00:26 -> 1624973400000)
                try:
                    ## a. today_ymd hms -> timestamp
                    ##    (2023-05-12 00:00:26 -> 1624973400)
                    timestamped = int(mktime(datetime.strptime(f'{today_ymd} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                    ## b. timestamp -> millisecond
                    ##    (1624973400 -> 1624973400000)
                    timestamped *= 1000

                    instance.milliseconds_and_counts[timestamped] = count
                except Exception:
                    pass

            instance.gidsids_and_counts           = Counter(map(itemgetter(2+1),  instance.rows))
            instance.descriptions_and_counts      = Counter(map(itemgetter(3+1),  instance.rows))
            instance.classifications_and_counts   = Counter(map(itemgetter(4+1),  instance.rows))
            instance.priorities_and_counts        = Counter(map(itemgetter(5+1),  instance.rows))
            instance.protocols_and_counts         = Counter(map(itemgetter(6+1),  instance.rows))
            instance.source_ips_and_counts        = Counter(map(itemgetter(7+1),  instance.rows))
            instance.source_ports_and_counts      = Counter(map(itemgetter(8+1),  instance.rows))
            instance.destination_ips_and_counts   = Counter(map(itemgetter(9+1),  instance.rows))
            instance.destination_ports_and_counts = Counter(map(itemgetter(10+1), instance.rows))

            ################################################

            instance.levels_and_counts = {
                'Critical': 0,
                'Warning':  0,
                'Low':      0,
                'Very Low': 0,
            }

            for classification_, count_ in instance.classifications_and_counts.items():
                if classification_ in SnortConfig.CLASSIFICATIONS__CRITICALS.value:
                    instance.levels_and_counts['Critical'] += count_
                elif classification_ in SnortConfig.CLASSIFICATIONS__WARNINGS.value:
                    instance.levels_and_counts['Warning'] += count_
                elif classification_ in SnortConfig.CLASSIFICATIONS__LOWS.value:
                    instance.levels_and_counts['Low'] += count_
                elif classification_ in SnortConfig.CLASSIFICATIONS__VERY_LOWS.value:
                    instance.levels_and_counts['Very Low'] += count_

            ################################################
            ## also in JUMP_5
            ## *toptable

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    for dictionary, table_name, key in [
                        ## dictionary                            table_name                 column/key
                        (instance.times_and_counts,             'timetoptable',            'Time'),
                        (instance.gidsids_and_counts,           'gidsidtoptable',          '`GID:SID`'),
                        (instance.descriptions_and_counts,      'descriptiontoptable',     'Description'),
                        (instance.classifications_and_counts,   'classificationtoptable',  'Classification'),
                        (instance.priorities_and_counts,        'prioritytoptable',        'Priority'),
                        (instance.protocols_and_counts,         'protocoltoptable',        'Protocol'),
                        (instance.source_ips_and_counts,        'sourceiptoptable',        '`Source IP`'),
                        (instance.source_ports_and_counts,      'sourceporttoptable',      '`Source Port`'),
                        (instance.destination_ips_and_counts,   'destinationiptoptable',   '`Destination IP`'),
                        (instance.destination_ports_and_counts, 'destinationporttoptable', '`Destination Port`'),

                        (instance.milliseconds_and_counts,      'millisecondtoptable',     'Millisecond'),
                        (instance.levels_and_counts,            'leveltoptable',           'Level'),
                    ]:
                        if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                            dictionary = {}

                        if not dictionary:
                            continue

                        if key in ['Time', 'Millisecond']:
                            sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                        else:
                            sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                        table_columns = f'''
                            ID    {MYSQLConfig.ID_DATA_TYPE.value},
                            {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                            Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                        table_keys = f'{key},Count'
                        table_marks = '%s,%s'

                        ## DROP table
                        save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                        cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                        cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                        cur.execute('START TRANSACTION;')
                        cur.executemany(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            tuple(sorted_dict.items())
                        )

                    conn.commit()

            ################################################

            ## __INDEXES_ONE_OFF__
            src_ips_and_dest_ips__tuple = map(itemgetter(7+1, 9+1), instance.rows)
            ## [
            ##     # source ip      destination ip
            ##     ('***********', '*******'),
            ##     ('***********', '*******'),
            ##     ...
            ## ]


            dest_ips_and_src_ips__dict = defaultdict(list)
            for src_ip, dest_ip in src_ips_and_dest_ips__tuple:
                if is_private(dest_ip):
                    continue

                ## commented because on some servers
                ## even when destination ips are public
                ## source ips my be public ips too
                # if not is_private(src_ip):
                #     continue

                dest_ips_and_src_ips__dict[dest_ip].append(src_ip)

            dest_ips_and_src_ips_counts = {
                k: sort_dict(Counter(v), based_on='value', reverse=True)
                for k, v
                in dest_ips_and_src_ips__dict.items()
            }
            ## {
            ##     '*******': {'***********': 1030},
            ##     '*******': {'***********': 2},
            ##     '*******': {'***********': 1660, '***********': 992, ...},
            ##     ...
            ## }


            dest_ips_and_src_ips_counts = sort_dict(dest_ips_and_src_ips_counts, based_on='key', reverse=False)

            ## IP        column contains destination ips
            ## IPsCounts column contains source ips + counts
            ##
            table_name = 'visitorsofiptable'
            table_columns = f'''
                ID          {MYSQLConfig.ID_DATA_TYPE.value},
                IP          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                IPsCounts   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                `No of IPs` {MYSQLConfig.COUNT_DATA_TYPE.value}'''
            table_keys = 'IP,IPsCounts,`No of IPs`'
            table_marks = '%s,%s,%s'

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(dest_ips_and_src_ips_counts):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    for k, v in dest_ips_and_src_ips_counts.items():
                        cur.execute(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            (k, dumps(v), len(v))
                            ## ^^ dumps(v) to turn dictionary of IPs into string (__USING_DUMPS_LOADS__)
                        )

                    conn.commit()

            ################################################
            instance.truncate_all()
            ################################################

            save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
            save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

            ################################################

            ## create accomplished_file
            # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

            sensor_end = perf_counter()
            sensor_duration = int(sensor_end - sensor_start)

            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {sensor_duration:,} seconds ({convert_second(seconds=sensor_duration, verbose=False)})')
            print(separator())

        print(end_of_command_msg(self, command))

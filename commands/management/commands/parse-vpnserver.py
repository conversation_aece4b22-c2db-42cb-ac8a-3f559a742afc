'''
NOTE this parser uses quick parsing method
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter, defaultdict
from datetime import datetime
from functools import partial
from json import dumps, loads
from multiprocessing import Pool, Process
from operator import itemgetter
from os import path, makedirs, remove
from shutil import rmtree
from signal import SIGINT, signal
from time import perf_counter, mktime
from typing import List, Optional, Tuple

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    convert_second,
    get_command,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.models import (
    Sensor,
    StaticIP,
)

from base.utils_extra import (
    get_vpnserver_object,
)

from base.utils_classes import (
    FilterLogConfig,
    MYSQLConfig,
    VPNServerConfig,
    VPNServerParser,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_parsers import (
    parse_ln,
)

from base.utils import (
    all_values_are_0,
    create_name_of_database,
    create_path_of_infile,
    end_of_command_msg,
    evenly_sized_batches,
    filter_list,
    get_date_of_source_log,
    get_no_of_infiles,
    get_today_ymd,
    hms_to_hourkey,
    is_invalid_log_date,
    separator,
    source_log_info_line,
    verbose_time_to_millisecond,
)


signal(SIGINT, keyboard_interrupt_handler)


def parse_line(ln: str, already_accomplished: List[str]) -> Tuple[Optional[str], Optional[str]]:
    '''
    Parses a given line and extracts relevant information.

    Args:
        ln (str): The input line to be parsed.
        already_accomplished (List[str]): A list of object names that have already been processed.

    Returns:
        Tuple[Optional[str], Optional[str]]: A tuple containing the object name and the parsed line.
        If the line is invalid or the object has already been processed, returns (None, None).
    '''

    _object_name, _parsed_ln = parse_ln(
        ln.strip(),
        VPNServerConfig,
        None,
        None,
    )

    # if _object_name in already_accomplished:
    #     return (None, None)

    return (_object_name, _parsed_ln)


class Command(BaseCommand):
    help = f'Parse {VPNServerConfig.TITLE.value}'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=False)

    def handle(self, *args, **kwargs):
        if not get_vpnserver_object():
            return abort(self, f'no {VPNServerConfig.SLUG.value} object.')

        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        #############################################################

        command = get_command(full_path=__file__, drop_extention=True)

        today_ymd = get_today_ymd()

        src_logs_dir = settings.LOGS_DIR
        if not path.exists(src_logs_dir):
            return abort(self, f'{src_logs_dir} does not exist.')

        source_logs = get_list_of_files(directory=src_logs_dir, extension='log')
        source_logs = filter_list(
            list_of_items=source_logs,

            year_months=year_months,
            year_month_days=year_month_days,

            start_year_month=start_year_month,
            start_year_month_day=start_year_month_day,

            end_year_month=end_year_month,
            end_year_month_day=end_year_month_day,
        )
        if not source_logs:
            return abort(self, 'no logs.')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            ## source_log may have been removed
            ## since the start of this command
            if not path.exists(source_log):
                print(colorize(self, 'error', f'{to_tilda(source_log)} does not exist. skipping parsing'))
                continue

            source_log_start = perf_counter()

            log_date = get_date_of_source_log(log_path=source_log)

            if is_invalid_log_date(log_date, today_ymd):
                continue



            ## check if log_date has already been accomplished
            if not force:
                if path.exists(f'{VPNServerConfig.get_logs_parsed_dir()}/{log_date}/{log_date}-accomplished.log'):
                    print(colorize(self, 'already_parsed', f'{command}: {log_date} already parsed, skipping'))
                    continue



            dest_dir          = f'{VPNServerConfig.get_logs_parsed_dir()}/{log_date}'
            accomplished_file = f'{dest_dir}/{log_date}-accomplished.log'
            log_file          = f'{dest_dir}/{log_date}.log'

            database_name = create_name_of_database(VPNServerConfig.SLUG.value, log_date)
            instance      = VPNServerParser(slug=VPNServerConfig.SLUG.value, ymd=log_date)

            print(source_log_info_line(source_log, source_log_index, len(source_logs)))

            ## pass multiple arguments in pool.map or pool.imap:
            ## parse_line takes 2 arguments:
            ## 1. first one (i.e. ln) will be passed by pool itself
            ##    down below when getting parsed_rows using pool
            ## 2. second one (i.e. already_accomplished) is passed here
            ## (https://medium.com/@deveshparmar248/python-multiprocessing-maximize-the-cpu-utilization-eec3b60e6d40)
            ## (https://python.omics.wiki/multiprocessing_map/multiprocessing_partial_function_multiple_arguments)
            parse_line__partialed = partial(parse_line, already_accomplished=None)

            with open(source_log, errors=ACTION_ON_ERROR) as lines:
                with Pool() as pool:
                    valid_lines = pool.imap(
                        func=parse_line__partialed,
                        iterable=lines,
                        chunksize=MYSQLConfig.POOL_CHUNKSIZE.value,
                    )

                    print('parsing...')
                    parse_start = perf_counter()
                    for object_name_, parsed_ln_ in valid_lines:
                        if not parsed_ln_:
                            continue

                        instance.rows.append(parsed_ln_)
                    parse_end = perf_counter()
                    parse_duration = int(parse_end - parse_start)
                    print(f'parsed in {parse_duration:,} seconds ({convert_second(seconds=parse_duration, verbose=False)})')

                    valid_lines = None
                    del valid_lines

                lines = None
                del lines

            ################################################

            ## remove and/or create dest_dir
            if path.exists(dest_dir):
                should_rm_dest_dir = False

                if force:
                    should_rm_dest_dir = True
                else:
                    if path.exists(accomplished_file):
                        print(colorize(self, 'already_parsed', f'{command}: {log_date} is already parsed. skipping'))
                        continue
                    else:
                        should_rm_dest_dir = True

                if should_rm_dest_dir:
                    print(colorize(self, 'removing', f'removing {to_tilda(dest_dir)}'))
                    rmtree(dest_dir)
                    print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                    makedirs(dest_dir)
            else:
                print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                makedirs(dest_dir)

            ################################################

            ## START __inserting_into_dbs__

            ## drop/create database
            with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                with conn.cursor() as cur:
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping database {database_name}')
                    cur.execute(f'DROP DATABASE IF EXISTS {database_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                    cur.execute(f'CREATE DATABASE {database_name};')

            ################################################

            ## update instance.rows:
            if instance.rows:
                ##  Step 1/2: get filterlog source/destination ips from database

                sensor_list_of_names = Sensor.get_list_of_names()

                filterlog_src_ips_and_dest_ips = defaultdict(set)

                for sensor in sensor_list_of_names:
                    database_name_ = create_name_of_database(FilterLogConfig.SLUG.value, log_date, sensor)
                    try:
                        log_msg = f'getting {FilterLogConfig.SLUG.value} source/destination ips from database {database_name_}'
                        save_log(self, command, settings.HOST_NAME, log_file, log_msg)
                        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name_) as conn:
                            with conn.cursor() as cur:
                                cur.execute('''
                                    SELECT `Source IP`, `Destination IPs`
                                    FROM sourceipsdestinationipstable
                                ;''')

                                for src_ip_, dest_ips_ in cur.fetchall():
                                    ## dest_ips_ is a string turned (by loads) into a list (__USING_DUMPS_LOADS__)
                                    filterlog_src_ips_and_dest_ips[src_ip_].update(loads(dest_ips_))
                    except Exception as exc:
                        error_msg = f'{exc!r}'
                        print(colorize(self, 'error', error_msg))
                        save_log(self, command, settings.HOST_NAME, log_file, error_msg, echo=False)

                ## filterlog_src_ips_and_dest_ips = {
                ##     '***********': {'*******', '*******', '*******'},
                ##     '***********': {'*******', '*******'},
                ##     ...
                ## }

                log_msg = f'got {len(filterlog_src_ips_and_dest_ips):,} {FilterLogConfig.SLUG.value} source/destination ips'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg)



                ## Step 2/2: add/convert items for instance.rows

                log_msg = 'adding source_ip and destination_ips, and converting active_for for each row'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                real_names_and_virtual_addresses = dict(StaticIP.active_objects.values_list('real_name', 'virtual_address'))
                ## {
                ##     'n.peterson': '*******',
                ##     'a.jackson': '*******',
                ##     ...
                ## }

                rows__tmp = []

                for r in instance.rows:
                    ## find source ip (e.g. ***********)
                    ## by username (e.g. n.peterson) which is r[3]
                    ## using StaticIP objects
                    source_ip_ = real_names_and_virtual_addresses.get(r[3]) or ''  ## ***********

                    if source_ip_:
                        ## {'*******', '*******'}
                        destination_ips_ = filterlog_src_ips_and_dest_ips.get(source_ip_, set())
                    else:
                        destination_ips_ = set()

                    rows__tmp.append((
                        r[0],  ## date
                        r[1],  ## time
                        r[2],  ## domain
                        r[3],  ## username
                        r[4],  ## port
                        verbose_time_to_millisecond(r[5]),  ## active for (227 minutes 14 seconds -> 45647465)
                        r[6],  ## sent
                        r[7],  ## received
                        source_ip_,
                        dumps(natsorted(destination_ips_)),
                        ## ^^ dumps() to turn set of destination ips into string (__USING_DUMPS_LOADS__)
                    ))

                instance.rows = rows__tmp

            ################################################
            ## *table

            ## __CHUNKED_INFILE__

            def write_into_infile(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles):
                _log_msg = f'  writing into {infile_path} ({infile_index}/{no_of_infiles}): {start_of_chunk:,} -> {end_of_chunk:,}'
                save_log(self, command, settings.HOST_NAME, log_file, _log_msg)

                _row_id = start_of_chunk
                with open(infile_path, 'w') as opened:
                    for _instance_row in instance.rows[start_of_chunk:end_of_chunk]:
                        _row_id += 1
                        opened.write(
                            ## ('a', 'b', 'c') -> "1"-*@*-"a"-*@*-"b"-*@*-"c"
                            f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                                map(
                                    lambda _cell:
                                    f'{MYSQLConfig.ENCLOSED_BY.value}{_cell}{MYSQLConfig.ENCLOSED_BY.value}',

                                    ## add ID to each row:
                                    ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                                    (_row_id,) + _instance_row,
                                )
                            )+'\n'
                        )

            no_of_infiles = get_no_of_infiles(length=instance.no_of_rows)

            if no_of_infiles:
                save_log(self, command, settings.HOST_NAME, log_file, f'{instance.no_of_rows:,} rows will be inserted into database')

                ## create table
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {VPNServerConfig.get_table_name()}')
                        cur.execute(f'CREATE TABLE {VPNServerConfig.get_table_name()} ({VPNServerConfig.DB_COLUMNS.value});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'{no_of_infiles} infiles will be created')

                        ## in each loop:
                        ## STEP 1: create n infiles at the same time
                        ## STEP 2: insert the n infiles into database one at a time
                        for batch_index, batch in enumerate(evenly_sized_batches(total_length=no_of_infiles), start=1):
                            save_log(self, command, settings.HOST_NAME, log_file, f'batch {batch_index}: writing into {len(batch)} infiles')

                            processes = []
                            infile_paths = []

                            ## STEP 1: create n infiles at the same time
                            for infile_index in batch:
                                infile_path = create_path_of_infile(database_name, VPNServerConfig.get_table_name(), infile_index)
                                start_of_chunk = MYSQLConfig.INFILE_CHUNKSIZE.value * (infile_index - 1)
                                end_of_chunk   = start_of_chunk + MYSQLConfig.INFILE_CHUNKSIZE.value

                                infile_paths.append(infile_path)
                                processes.append(
                                    Process(
                                        target=write_into_infile,
                                        args=(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles)
                                    )
                                )

                            for p in processes:
                                p.start()

                            for p in processes:
                                p.join()

                            ## STEP 2: insert the n infiles into database one at a time
                            log_msg = f'batch {batch_index}: inserting into {VPNServerConfig.get_table_name()} from {len(infile_paths)} infiles'
                            save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                            for infile_idx, infile_path in enumerate(natsorted(infile_paths), start=1):
                                if not path.exists(infile_path):
                                    continue

                                save_log(self, command, settings.HOST_NAME, log_file, f'  inserting from {infile_path}')
                                cur.execute('SET UNIQUE_CHECKS=0;')
                                cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                                ##
                                ## __TRANSACTION__
                                ## 'START TRANSACTION' preferred over
                                ## 'BEGIN' and 'BEGIN WORK':
                                ##  (https://stackoverflow.com/a/62614872/)
                                cur.execute('START TRANSACTION;')
                                cur.execute(f'''
                                    {MYSQLConfig.get_infile_statement()} "{infile_path}"
                                    INTO TABLE {VPNServerConfig.get_table_name()}
                                    FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                                    ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                                    LINES TERMINATED BY "\n"
                                    (ID,{VPNServerConfig.DB_KEYS.value})
                                ;''')

                            ## __TRANSACTION__ commit after loop
                            save_log(self, command, settings.HOST_NAME, log_file, '  committing...')
                            conn.commit()

                            for infile_path in infile_paths:
                                ## remove infile
                                save_log(self, command, settings.HOST_NAME, log_file, f'  removing {infile_path}')
                                remove(infile_path)

                        ## just in case
                        processes = None
                        infile_paths = None
                        del processes
                        del infile_paths

            ################################################
            ## *_and_counts

            ## __INDEXES_ONE_OFF__
            ## the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
            ## compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
            ## in hourly-parse-<APP_SLUG>.py,
            ## instance.rows are directly read from *table in database
            ## meaning ID column is also included
            ## so we have to increment indexes by 1
            ## to get the right columns

            save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

            for hms, count in Counter(map(itemgetter(1), instance.rows)).items():
                ## {'00:49:51': 12, '02:59:55': 1182, ...}
                ## ->
                ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                instance.times_and_counts[hms_to_hourkey(hms)] += count

                ## -----

                ## log_date hms -> millisecond
                ## (2023-05-12 00:00:26 -> 1624973400000)
                try:
                    ## a. log_date hms -> timestamp
                    ##    (2023-05-12 00:00:26 -> 1624973400)
                    timestamped = int(mktime(datetime.strptime(f'{log_date} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                    ## b. timestamp -> millisecond
                    ##    (1624973400 -> 1624973400000)
                    timestamped *= 1000

                    instance.milliseconds_and_counts[str(timestamped)] = count
                except Exception:
                    pass

            instance.domains_and_counts    = Counter(map(itemgetter(2), instance.rows))
            instance.usernames_and_counts  = Counter(map(itemgetter(3), instance.rows))
            instance.ports_and_counts      = Counter(map(itemgetter(4), instance.rows))
            instance.source_ips_and_counts = Counter(map(itemgetter(8), instance.rows))

            ################################################
            ## *toptable

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    for dictionary, table_name, key in [
                        ## dictionary                        table_name             column/key
                        (instance.times_and_counts,         'timetoptable',        'Time'),
                        (instance.domains_and_counts,       'domaintoptable',      'Domain'),
                        (instance.usernames_and_counts,     'usernametoptable',    'Username'),
                        (instance.ports_and_counts,         'porttoptable',        'Port'),
                        (instance.source_ips_and_counts,    'sourceiptoptable',    '`Source IP`'),

                        (instance.milliseconds_and_counts,  'millisecondtoptable', 'Millisecond'),
                    ]:
                        if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                            dictionary = {}

                        if not dictionary:
                            continue

                        if key in ['Time', 'Millisecond']:
                            sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                        else:
                            sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                        table_columns = f'''
                            ID    {MYSQLConfig.ID_DATA_TYPE.value},
                            {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                            Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                        table_keys = f'{key},Count'
                        table_marks = '%s,%s'

                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                        cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                        cur.execute('START TRANSACTION;')
                        cur.executemany(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            tuple(sorted_dict.items())
                        )

                    conn.commit()

            ################################################
            ## __INDEXES_ONE_OFF__

            instance.username_aggregates = {
                usr_nam_: {
                    'active_for': 0,
                    'sent': 0,
                    'received': 0,
                }
                for usr_nam_, _ in instance.usernames_and_counts.items()
            }

            temp_list_1 = map(itemgetter(3, 5, 6, 7), instance.rows)
            ## temp_list_1 = [
            ##     ('n.peterson', '3536', '34983', '36574'),
            ##     ('b.jackson', '1234', '54363738', '267'),
            ##     ...
            ## ]
            ##
            for username_, active_for_, sent_, received_ in temp_list_1:
                instance.username_aggregates[username_]['active_for'] += int(active_for_)
                instance.username_aggregates[username_]['sent']       += int(sent_)
                instance.username_aggregates[username_]['received']   += int(received_)
            ## instance.username_aggregates = {
            ##     'n.peterson': {
            ##         'active_for': 34637847,
            ##         'sent': 373839438,
            ##         'received': 27389273948,
            ##     },
            ##     ...
            ## }

            ################################################
            instance.username_aggregates = sort_dict(instance.username_aggregates, based_on='key', reverse=False)

            table_name = 'useraggregatestable'
            table_columns = f'''
                ID           {MYSQLConfig.ID_DATA_TYPE.value},
                Username     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                `Active For` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                Sent         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                Received     {MYSQLConfig.DEFAULT_DATA_TYPE.value}'''
            table_keys = 'Username,`Active For`,Sent,Received'
            table_marks = '%s,%s,%s,%s'
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.username_aggregates):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    for username_, info_ in instance.username_aggregates.items():
                        cur.execute(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            (
                                username_,
                                info_['active_for'],
                                info_['sent'],
                                info_['received'],
                            )
                        )
                    conn.commit()

            ################################################
            instance.truncate_all()
            ################################################

            save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
            save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

            ################################################

            ## create accomplished_file
            save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

            source_log_end = perf_counter()
            source_log_duration = int(source_log_end - source_log_start)

            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {source_log_duration:,} seconds ({convert_second(seconds=source_log_duration, verbose=False)})')
            print(separator())

            ## END __inserting_into_dbs__

        print(end_of_command_msg(self, command))

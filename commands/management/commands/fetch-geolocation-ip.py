from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from json import loads
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter

import asyncio

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

import httpx

from base.utils_classes import (
    GeoLocationParser,
    GeoLocationConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    HTTP_HEADERS,
    MAX_TRIES,
    PRINT_ENDPOINT,
    TIMEOUTS,
)

from base.utils_database import (
    get_all_ips,
)

from base.utils_tor import (
    check_is_tor,
    renew_tor_identity,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    list_of_tuples_to_list,
    service_is_running,
)


signal(SIGIN<PERSON>, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Fetch {GeoLocationConfig.TITLE.value} - IP'

    def add_arguments(self, parser):
        parser.add_argument(
            '-p',
            '--proxy',
            default=False,
            action='store_true',
            help='Use proxy',
        )

    def handle(self, *args, **kwargs):
        proxy = kwargs.get('proxy')

        ################################################

        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        httpx_timeout = httpx.Timeout(TIMEOUTS.fetcher)

        database_name = create_name_of_database(GeoLocationConfig.SLUG.value)

        src_dir = GeoLocationConfig.get_logs_parsed_dir()
        log_file = f'{src_dir}/log--ip'

        instance = GeoLocationParser(slug=GeoLocationConfig.SLUG.value)

        if not path.exists(src_dir):
            print(colorize(self, 'creating', f'creating {to_tilda(src_dir)}'))
            makedirs(src_dir)

        start = perf_counter()

        ################################################

        is_tor = False
        tor_ip = ''

        if proxy:
            save_log(self, command, settings.HOST_NAME, log_file, 'checking tor availability')

            ## check 1
            tor_is_running = service_is_running('tor')
            if not tor_is_running:
                log_msg = 'proxy requested but tor service is not running'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)

            ## check 2
            is_tor, tor_ip = check_is_tor(self)  ## (True, '*******')
            if not is_tor:
                log_msg = 'proxy requested but tor could not be connected (i.e. check_is_tor() returned False)'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)

            save_log(self, command, settings.HOST_NAME, log_file, f'proxy set to tor. ip: {tor_ip}')
            proxy = settings.TOR_PROXY
        else:
            proxy = None

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'getting all ips')

        ips = get_all_ips(unique=True, public_only=True)

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'getting ips that already have geolocation')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        SELECT IP
                        FROM {GeoLocationConfig.get_table_name(geo_mode="ip")}
                    ;''')
                    ## using set to have faster difference in JUMP_4
                    ips__already = set(list_of_tuples_to_list(cur.fetchall()))
        except Exception as exc:
            save_log(self, command, settings.HOST_NAME, log_file, f'ERROR: {exc!r}')
            ips__already = set()

        ################################################

        ## JUMP_4 remove items of ips__already from ips
        ## (https://stackoverflow.com/a/54297667/)
        if ips__already:
            save_log(self, command, settings.HOST_NAME, log_file, 'removing ips that already have geolocation from list')

            ## making sure it's a list
            ## because evenly_sized_chunks throws an error
            ## if it is a set
            ips = list(
                set(ips).difference(ips__already)  ## gives a set
            )


        if not ips:
            return abort(self, 'no ips. exiting')

        ################################################

        ## create database
        with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
            with conn.cursor() as cur:
                save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                cur.execute(f'CREATE DATABASE IF NOT EXISTS {database_name};')

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'fetching domains')

        global should_renew_tor, should_abort, renew_counts
        should_renew_tor = False
        should_abort      = False
        renew_counts     = 1


        def insert_into_db(rows):
            if not rows:
                return

            save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(rows):,} rows into {GeoLocationConfig.get_table_name(geo_mode="ip")}')

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        CREATE TABLE IF NOT EXISTS {GeoLocationConfig.get_table_name(geo_mode="ip")}
                        ({GeoLocationConfig.DB_COLUMNS__IP.value})
                    ;''')

                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {GeoLocationConfig.get_table_name(geo_mode="ip")} ({GeoLocationConfig.DB_KEYS__IP.value}) VALUES ({GeoLocationConfig.DB_MARKS__IP.value});',
                        rows,
                    )
                    conn.commit()

        def evenly_sized_chunks(ips, len_of_each_chunk=20):
            ''' NOTE a similar structure in evenly_sized_batches() in base/utils.py
            https://www.programiz.com/python-programming/examples/list-chunks

            takes list of ips       e.g. ['*******', '*******', ..., '********']
            takes len_of_each_chunk e.g. 10

            returns generator: <generator object ... at ...>
            which looks like this if turned into list:
            [
                ['*******',  ..., '*******0'],
                ['*******1', ..., '*******0'],
                ['*******1', ..., '********'],
                ['********', ..., '********'],
                ['********', '********'],
            ]

            '''
            for i in range(0, len(ips), len_of_each_chunk):
                yield ips[i:i + len_of_each_chunk]

        async def fetch_one(client, ip):
            global should_renew_tor, renew_counts, should_abort

            if should_renew_tor:
                if proxy:
                    save_log(self, command, settings.HOST_NAME, log_file, f'renewing tor identity (renew counts: {renew_counts})')
                    renew_tor_identity()
                    await asyncio.sleep(2)

                    save_log(self, command, settings.HOST_NAME, log_file, '  renewed. getting new tor ip...')
                    is_tor, tor_ip = check_is_tor(self)  ## (True, '*******')

                    save_log(self, command, settings.HOST_NAME, log_file, f'  new tor ip: {tor_ip}')

                    renew_counts += 1
                    should_renew_tor = False
                else:
                    should_abort = True
                    return None

            pre_msg = ip
            print(pre_msg, end=PRINT_ENDPOINT)

            dl_try = 0
            dl_successful = False

            while not dl_successful and dl_try < MAX_TRIES.dl:
                dl_try += 1
                is_valid_domain = False
                row = tuple()

                response_text = ''
                success = False
                message = None

                try:
                    ## JUMP_1
                    response = await client.get(
                        ## no trailing /
                        url=f'{settings.GEOLOCATION_URL__IP}/{ip}',

                        headers=HTTP_HEADERS,
                        timeout=httpx_timeout,
                    )

                    response.raise_for_status()

                    response_text = response.text  ## JUMP_2
                    response_dict = loads(response_text)

                    success = response_dict.get('success', False)  ## True/False
                    message = response_dict.get('message')

                    ## JUMP_3
                    row = (
                        ip,
                        response_dict.get('type', ''),
                        response_dict.get('continent', ''),
                        response_dict.get('continent_code', ''),
                        response_dict.get('country', ''),  ## United States
                        response_dict.get('country_code', ''),
                        response_dict.get('city', ''),
                        response_dict.get('latitude', ''),
                        response_dict.get('longitude', ''),
                        'Yes' if response_dict.get('is_eu', False) else 'No',
                        response_dict.get('connection', {}).get('asn', ''),
                        response_dict.get('connection', {}).get('org', ''),
                        response_dict.get('connection', {}).get('isp', ''),
                        response_dict.get('connection', {}).get('domain', ''),  ## amazon.com
                        response_dict.get('timezone', {}).get('id', ''),
                        response_dict.get('flag', {}).get('emoji', ''),
                        response_dict.get('flag', {}).get('emoji_unicode', ''),
                        response_dict.get('flag', {}).get('img', ''),
                    )

                    country_ = row[4]
                    domain_  = row[13]

                    ## NOTE __IS_VALID_DOMAIN__
                    ## when success is True or message is 'Reserved range',
                    ## we do 2 things:
                    ##   1. set dl_successful to True
                    ##      to stop tries
                    ##   2. set is_valid_domain to True
                    ##      to accept domain_'s value
                    ##      as a valid value whatever it is
                    ##      (i.e. '' or 'microsoft.com')

                    if success:
                        if domain_:
                            print(f'{pre_msg}: {colorize(self, "success", domain_)} {country_}')
                        else:
                            print(f'{pre_msg}: {colorize(self, "warning", "no domain")}')
                        dl_successful = True
                        is_valid_domain = True  ## __IS_VALID_DOMAIN__

                    ## response_dict is {'success': False, 'message': 'You've hit the monthly limit'}
                    elif 'hit the monthly limit' in message:
                        print(f'{pre_msg}: {colorize(self, "error", "message: hit the monthly limit")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    ## response_dict is {'ip': '********', 'success': False, 'message': 'Reserved range'}
                    elif message == 'Reserved range':
                        print(f'{pre_msg}: {colorize(self, "error", "message: Reserved range")}')
                        dl_successful = True
                        is_valid_domain = True  ## __IS_VALID_DOMAIN__

                        ## JUMP_3
                        row = (
                            ip,
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                            'Reserved range',
                        )
                    else:
                        print(f'{pre_msg}: {colorize(self, "error", f"failed. message: {message}")}')
                        await asyncio.sleep(1)

                except httpx.HTTPStatusError as exc:
                    response_status_code = exc.response.status_code
                    print(f'{pre_msg}: {colorize(self, "error", f"status code: {response_status_code}")}')

                    if response_status_code == 404:
                        dl_successful = True

                    elif response_status_code == 429:
                        ## response code 429 indicates the client
                        ## has sent too many requests in a given amount of time
                        await asyncio.sleep(3)

                except Exception as exc:
                    str_exc = str(exc)

                    if 'Invalid non-printable ASCII character in URL' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Invalid non-printable ASCII character in URL")}')
                        dl_successful = True

                    elif 'Proxy Server could not connect: TTL expired' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Proxy Server could not connect: TTL expired")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    elif 'Proxy Server could not connect: General SOCKS server failure' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Proxy Server could not connect: General SOCKS server failure")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    ## it throws JSONDecideError
                    ## when it cantt loads() the response, because:
                    ##
                    ## 1. response_text may be:
                    ##    <br />
                    ##    <b>Notice</b>:  Undefined index: ip in <b>/var/www/www-root/data/www/ipwho.is/index.php</b> on line <b>160</b><br />
                    ##    {"success":false,"message":"Rate limit (per second) for free endpoint exceeded"}
                    elif 'Rate limit (per second) for free endpoint exceeded' in response_text:
                        print(f'{pre_msg}: {colorize(self, "error", "Rate limit (per second) for free endpoint exceeded")}')
                    ##
                    ## 2. response_text may be:
                    ##    <br />
                    ##    <b>Notice</b>:  Undefined index: HTTP_X_RAPIDAPI_PROXY_SECRET in <b>/var/www/www-root/data/www/ipwho.is/index_mysql.php</b> on line <b>24</b><br />
                    ##    {"success":false,"message":"You've hit the monthly limit"}
                    elif 'hit the monthly limit' in response_text:
                        print(f'{pre_msg}: {colorize(self, "error", "message: hit the monthly limit")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    else:
                        print(f'{pre_msg}: {colorize(self, "error", f"{exc!r}")}')

                    await asyncio.sleep(1)


            ## __IS_VALID_DOMAIN__
            if is_valid_domain and row:
                return row

            return None

        async def fetch_all(ips):
            async with httpx.AsyncClient(proxy=proxy) as client:
                tasks = [
                    asyncio.create_task(fetch_one(client, ip))
                    for ip in ips
                ]
                return await asyncio.gather(*tasks)

        for chunk_index, chunk in enumerate(evenly_sized_chunks(ips), start=1):
            instance.fetched_all = asyncio.run(fetch_all(ips=chunk))

            ## remove None items
            instance.fetched_all = [
                _ for _ in instance.fetched_all
                if _ is not None
            ]

            if instance.fetched_all:
                insert_into_db(instance.fetched_all)

            if should_abort:
                log_msg = 'should abort'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)

        ################################################

        ## now that we have updated the database
        ## with the newly-fetched data,
        ## let's get the instance.rows before moving on to *_and_counts

        save_log(self, command, settings.HOST_NAME, log_file, f'getting rows from database {database_name}')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    ## using GeoLocationConfig.DB_KEYS__IP.value
                    ## instead of GeoLocationConfig.get_select_statement()
                    ## because we do not need the ID column
                    cur.execute(f'SELECT {GeoLocationConfig.DB_KEYS__IP.value} FROM {GeoLocationConfig.get_table_name(geo_mode="ip")};')
                    instance.rows = cur.fetchall()
        except Exception as exc:
            save_log(self, command, settings.HOST_NAME, log_file, f'ERROR: {exc!r}')
            instance.rows = []

        ################################################
        ## *_and_counts

        ## __INDEXES_ONE_OFF__
        ## the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
        ## compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
        ## in hourly-parse-<APP_SLUG>.py,
        ## instance.rows are directly read from *table in database
        ## meaning ID column is also included
        ## so we have to increment indexes by 1
        ## to get the right columns

        save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

        instance.types_and_counts         = Counter(map(itemgetter(1),  instance.rows))
        instance.continents_and_counts    = Counter(map(itemgetter(2),  instance.rows))
        instance.countries_and_counts     = Counter(map(itemgetter(4),  instance.rows))
        instance.cities_and_counts        = Counter(map(itemgetter(6),  instance.rows))
        instance.organizations_and_counts = Counter(map(itemgetter(11), instance.rows))
        instance.isps_and_counts          = Counter(map(itemgetter(12), instance.rows))
        instance.domains_and_counts       = Counter(map(itemgetter(13), instance.rows))
        instance.timezones_and_counts     = Counter(map(itemgetter(14), instance.rows))

        ################################################
        ## *toptable

        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key in [
                    ## dictionary                        table_name                  column/key
                    (instance.types_and_counts,         'typetoptable__ip',         'Type'),
                    (instance.continents_and_counts,    'continenttoptable__ip',    'Continent'),
                    (instance.countries_and_counts,     'countrytoptable__ip',      'Country'),
                    (instance.cities_and_counts,        'citytoptable__ip',         'City'),
                    (instance.organizations_and_counts, 'organizationtoptable__ip', 'Organization'),
                    (instance.isps_and_counts,          'isptoptable__ip',          'ISP'),
                    (instance.domains_and_counts,       'domaintoptable__ip',       'Domain'),
                    (instance.timezones_and_counts,     'timezonetoptable__ip',     'Timezone'),
                ]:
                    if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                        dictionary = {}

                    if not dictionary:
                        continue

                    if key in ['Time', 'Millisecond']:
                        sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                    else:
                        sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                    table_columns = f'''
                        ID    {MYSQLConfig.ID_DATA_TYPE.value},
                        {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                    table_keys = f'{key},Count'
                    table_marks = '%s,%s'

                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        tuple(sorted_dict.items())
                    )

                conn.commit()

        ################################################
        instance.truncate_all()
        ################################################

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'fetched in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')

        print(end_of_command_msg(self, command))


''' JUMP_1
{
  "ip": "**********",
  "success": true,
  "type": "IPv4",
  "continent": "Europe",
  "continent_code": "EU",
  "country": "United Kingdom",
  "country_code": "GB",
  "region": "England",
  "region_code": "ENG",
  "city": "London",
  "latitude": 51.5073509,
  "longitude": -0.1277583,
  "is_eu": false,
  "postal": "SW1",
  "calling_code": "44",
  "capital": "London",
  "borders": "IE",
  "flag": {
    "img": "https://cdn.ipwhois.io/flags/gb.svg",
    "emoji": "🇬🇧",
    "emoji_unicode": "U+1F1EC U+1F1E7"
  },
  "connection": {
    "asn": 20940,
    "org": "Akamai Technologies",
    "isp": "Akamai International B.V.",
    "domain": "akamai.com"
  },
  "timezone": {
    "id": "Europe/London",
    "abbr": "GMT",
    "is_dst": false,
    "offset": 0,
    "utc": "+00:00",
    "current_time": "2024-12-16T19:42:46+00:00"
  }
}
'''

''' JUMP_2
{'borders': 'IE',
 'calling_code': '44',
 'capital': 'London',
 'city': 'London',
 'connection': {'asn': 20940,
                'domain': 'akamai.com',
                'isp': 'Akamai International B.V.',
                'org': 'Akamai Technologies'},
 'continent': 'Europe',
 'continent_code': 'EU',
 'country': 'United Kingdom',
 'country_code': 'GB',
 'flag': {'emoji': '🇬🇧',
          'emoji_unicode': 'U+1F1EC U+1F1E7',
          'img': 'https://cdn.ipwhois.io/flags/gb.svg'},
 'ip': '**********',
 'is_eu': False,
 'latitude': 51.5073509,
 'longitude': -0.1277583,
 'postal': 'SW1',
 'region': 'England',
 'region_code': 'ENG',
 'success': True,
 'timezone': {'abbr': 'GMT',
              'current_time': '2024-12-16T19:47:44+00:00',
              'id': 'Europe/London',
              'is_dst': False,
              'offset': 0,
              'utc': '+00:00'},
 'type': 'IPv4'}
'''

/*
NOTE variables starting with _
     won't be used outside this module
*/

use once_cell::sync::Lazy;
use std::sync::Once;
use std::collections::HashMap;

use dotenv::from_path;
use std::env;
use std::path::Path;

use crate::snort_classifications::{
    CLASSIFICATIONS_DICT,
    CLASSIFICATIONS__CRITICALS,
    CLASSIFICATIONS__WARNINGS,
    CLASSIFICATIONS__LOWS,
    CLASSIFICATIONS__VERY_LOWS,
};

use crate::windows_server_audit_events::WINDOWS_SERVER_AUDIT_EVENTS;


pub const HOUR_KEYS: [&str; 24] = [
    "00:00 - 00:59",
    "01:00 - 01:59",
    "02:00 - 02:59",
    "03:00 - 03:59",
    "04:00 - 04:59",
    "05:00 - 05:59",
    "06:00 - 06:59",
    "07:00 - 07:59",
    "08:00 - 08:59",
    "09:00 - 09:59",
    "10:00 - 10:59",
    "11:00 - 11:59",
    "12:00 - 12:59",
    "13:00 - 13:59",
    "14:00 - 14:59",
    "15:00 - 15:59",
    "16:00 - 16:59",
    "17:00 - 17:59",
    "18:00 - 18:59",
    "19:00 - 19:59",
    "20:00 - 20:59",
    "21:00 - 21:59",
    "22:00 - 22:59",
    "23:00 - 23:59",
];

// https://community.cisco.com/t5/other-network-architecture-subjects/what-is-logging-facility-local7/td-p/373768
pub const EVENT_TYPES: [&str; 8] = [
    "(local7/alert)",
    "(local7/crit)",
    "(local7/debug)",
    "(local7/emerg)",
    "(local7/err)",
    "(local7/info)",
    "(local7/notice)",
    "(local7/warning)",
];
//
pub static EVENT_TYPES__CRITICALS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    EVENT_TYPES
    .iter()
    .cloned()
    .filter(|e| {
        e.contains("/alert")
            || e.contains("/crit")
            || e.contains("/emerg")
            || e.contains("/err")
    })
    .collect()
});

pub static EVENT_TYPES__WARNINGS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    EVENT_TYPES
    .iter()
    .cloned()
    .filter(|e| e.contains("/warning"))
    .collect()
});

// https://community.cisco.com/t5/other-network-architecture-subjects/what-is-logging-facility-local7/td-p/373768
pub const _EVENT_TYPES__DAEMON: [&str; 8] = [
    "(daemon/alert)",
    "(daemon/crit)",
    "(daemon/debug)",
    "(daemon/emerg)",
    "(daemon/err)",
    "(daemon/info)",
    "(daemon/notice)",
    "(daemon/warning)",
];
//
pub static _EVENT_TYPES__DAEMON__CRITICALS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    _EVENT_TYPES__DAEMON
    .iter()
    .cloned()
    .filter(|e| {
        e.contains("/alert")
            || e.contains("/crit")
            || e.contains("/emerg")
            || e.contains("/err")
    })
    .collect()
});
//
pub static _EVENT_TYPES__DAEMON__WARNINGS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    _EVENT_TYPES__DAEMON
    .iter()
    .cloned()
    .filter(|e| e.contains("/warning"))
    .collect()
});

// -------------------------------

// load .env
static INIT_ENV: Once = Once::new();
fn ensure_env_loaded() {
    INIT_ENV.call_once(|| {
        let env_path = Path::new("../.env");
        from_path(env_path).expect("Failed to load .env");
    });
}

// -------------------------------
// *Config

#[allow(non_camel_case_types)]
#[derive(Debug, Clone)]
pub enum MYSQLValue {
    Str(String),
    Int(usize),
    Bool(bool),
    List(Vec<String>),
    Tuple(Vec<(String, String)>),
    Dict(std::collections::HashMap<String, String>),
    ClassificationDict(std::collections::HashMap<String, &'static super::snort_classifications::ClassificationInfo>),
}

impl MYSQLValue {
    pub fn as_list_of_strings(self) -> Vec<String> {
        match self {
            MYSQLValue::List(list) => list,
            _ => vec![],
        }
    }

    pub fn value_string(self) -> String {
        match self {
            MYSQLValue::Str(s) => s,
            MYSQLValue::Int(i) => i.to_string(),
            MYSQLValue::Bool(b) => b.to_string(),
            _ => panic!("Expected str, int or bool, found other type"),
        }
    }

    pub fn value_int(self) -> usize {
        match self {
            MYSQLValue::Int(i) => i,
            _ => panic!("Expected int, found other type"),
        }
    }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum MYSQLConfig {
    ID_DATA_TYPE,
    DATE_DATA_TYPE,
    TIME_DATA_TYPE,
    DEFAULT_DATA_TYPE,
    INDEXED_COLUMN_DATA_TYPE,
    COUNT_DATA_TYPE,
    BUILTIN_DATABASES,
    INFILE_CHUNKSIZE,
    ENCLOSED_BY,
    INDEX_PREFIX_LENGTH,
    INDEX_TYPE,
    TERMINATED_BY,
    DB_NAME_SEPARATOR,
    TABLE_NAME_SEPARATOR,
    POOL_CHUNKSIZE,
    NON_DATED_DATABASES,

    MYSQL_HOST,
    MYSQL_MASTER,
    MYSQL_MASTER_PASSWD,

    MYSQL_R_USER,
    MYSQL_R_USER_PASSWD,
}

impl MYSQLConfig {
    pub fn value(&self) -> MYSQLValue {
        // ✅ always loads .env first
        ensure_env_loaded();

        match self {
            Self::ID_DATA_TYPE             => MYSQLValue::Str("INT PRIMARY KEY AUTO_INCREMENT".to_string()),
            Self::DATE_DATA_TYPE           => MYSQLValue::Str("VARCHAR(10)".to_string()),
            Self::TIME_DATA_TYPE           => MYSQLValue::Str("VARCHAR(13)".to_string()),
            Self::DEFAULT_DATA_TYPE        => MYSQLValue::Str("MEDIUMTEXT".to_string()),
            Self::INDEXED_COLUMN_DATA_TYPE => MYSQLValue::Str("VARCHAR(5000)".to_string()),
            Self::COUNT_DATA_TYPE          => MYSQLValue::Str("INT".to_string()),

            Self::BUILTIN_DATABASES => MYSQLValue::List(vec![
                "information_schema".to_string(),
                "mysql".to_string(),
                "performance_schema".to_string(),
                "sys".to_string(),
            ]),

            Self::INFILE_CHUNKSIZE     => MYSQLValue::Int(5_000_000),
            Self::ENCLOSED_BY          => MYSQLValue::Str("".to_string()),  // '"' caused errors
            Self::INDEX_PREFIX_LENGTH  => MYSQLValue::Int(100),  // changed from 50 for better text field indexing
            Self::INDEX_TYPE           => MYSQLValue::Str("BTREE".to_string()),  // changed from HASH for better range query support
            Self::TERMINATED_BY        => MYSQLValue::Str("-*@*-".to_string()),
            Self::DB_NAME_SEPARATOR    => MYSQLValue::Str("__".to_string()),  // __DATABASE_YMD_PATTERN__
            Self::TABLE_NAME_SEPARATOR => MYSQLValue::Str("__".to_string()),

            Self::POOL_CHUNKSIZE => MYSQLValue::Int(100_000),

            // loaded from .env
            // __FIXME__ use as MASTER_CREDS and R_USER_CREDS dictionaries
            //
            // master
            Self::MYSQL_HOST => MYSQLValue::Str(env::var("MYSQL_HOST")
                .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"))
            ),
            Self::MYSQL_MASTER => MYSQLValue::Str(env::var("MYSQL_MASTER")
                .unwrap_or_else(|_| panic!("Missing MYSQL_MASTER in .env"))
            ),
            Self::MYSQL_MASTER_PASSWD => MYSQLValue::Str(env::var("MYSQL_MASTER_PASSWD")
                .unwrap_or_else(|_| panic!("Missing MYSQL_MASTER_PASSWD in .env"))
            ),
            //
            // r user
            Self::MYSQL_R_USER => MYSQLValue::Str(env::var("MYSQL_R_USER")
                .unwrap_or_else(|_| panic!("Missing MYSQL_R_USER in .env"))
            ),
            Self::MYSQL_R_USER_PASSWD => MYSQLValue::Str(env::var("MYSQL_R_USER_PASSWD")
                .unwrap_or_else(|_| panic!("Missing MYSQL_R_USER_PASSWD in .env"))
            ),

            Self::NON_DATED_DATABASES  => MYSQLValue::List(vec![
                "geolocation".to_string(),  // JUMP_13
                "malicious".to_string(),  // JUMP_14
            ]),
        }
    }

    pub fn get_infile_statement() -> &'static str {
        ensure_env_loaded();  // Optional, in case DEBUG is loaded from .env

        match env::var("DEBUG").as_deref() {
            Ok("True") | Ok("true") | Ok("1") => "LOAD DATA LOCAL INFILE",
            _ => "LOAD DATA INFILE",
        }
    }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum GeoLocationConfig {
    TITLE,
    SLUG,

    // DB_HEADERS_WITH_INDEXES__DOMAIN,
    DB_HEADERS__DOMAIN,
    DB_COLUMNS__DOMAIN,
    DB_KEYS__DOMAIN,
    DB_MARKS__DOMAIN,

    // DB_HEADERS_WITH_INDEXES__IP,
    DB_HEADERS__IP,
    DB_COLUMNS__IP,
    DB_KEYS__IP,
    DB_MARKS__IP,

    // COUNTRY_CODES_DICT,
}

impl GeoLocationConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            GeoLocationConfig::TITLE => MYSQLValue::Str("GeoLocation".to_string()),
            GeoLocationConfig::SLUG => MYSQLValue::Str("geolocation".to_string()),  // JUMP_13

            // commented because not to be used in rust
            // GeoLocationConfig::DB_HEADERS_WITH_INDEXES__DOMAIN => ...

            GeoLocationConfig::DB_HEADERS__DOMAIN => MYSQLValue::List(vec![
                "ID".to_string(),
                "Domain".to_string(),
                "Country".to_string(),
                "Country Code".to_string(),
                "Region".to_string(),
                "City".to_string(),
                "Latitude".to_string(),
                "Longitude".to_string(),
                "Timezone".to_string(),
                "ISP".to_string(),
                "Organization".to_string(),
                "IP".to_string(),
            ]),

            GeoLocationConfig::DB_COLUMNS__DOMAIN => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID             {id_data_type},
                    Domain         {default_data_type},
                    Country        {default_data_type},
                    `Country Code` {default_data_type},
                    Region         {default_data_type},
                    City           {default_data_type},
                    Latitude       {default_data_type},
                    Longitude      {default_data_type},
                    Timezone       {default_data_type},
                    ISP            {default_data_type},
                    Organization   {default_data_type},
                    IP             {default_data_type}
                    "
                ))
            }

            GeoLocationConfig::DB_KEYS__DOMAIN => MYSQLValue::Str(
                "
                Domain,
                Country,
                `Country Code`,
                Region,
                City,
                Latitude,
                Longitude,
                Timezone,
                ISP,
                Organization,
                IP
                "
                .to_string(),
            ),

            GeoLocationConfig::DB_MARKS__DOMAIN => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),

            // commented because not to be used in rust
            // GeoLocationConfig::DB_HEADERS_WITH_INDEXES__IP => ...

            GeoLocationConfig::DB_HEADERS__IP => MYSQLValue::List(vec![
                "ID".to_string(),
                "IP".to_string(),                  // ***********
                "Type".to_string(),                // IPv4
                "Continent".to_string(),           // Europe
                "Continent Code".to_string(),      // EU
                "Country".to_string(),             // Romania
                "Country Code".to_string(),        // RO
                "City".to_string(),                // Suceava
                "Latitude".to_string(),            // 47.6634521
                "Longitude".to_string(),           // 26.2732302
                "Is EU".to_string(),               // true
                "ASN".to_string(),                 // 8708
                "Organization".to_string(),        // Rcs Rds Residential
                "ISP".to_string(),                 // DIGI ROMANIA S.A.
                "Domain".to_string(),              // digi.ro
                "Timezone".to_string(),            // Europe/Bucharest
                "Flag Emoji".to_string(),          // 🇷🇴
                "Flag Emoji Unicode".to_string(),  // U+1F1F7 U+1F1F4
                "Flag IMG".to_string(),            // https://cdn.ipwhois.io/flags/ro.svg
            ]),

            GeoLocationConfig::DB_COLUMNS__IP => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                   {id_data_type},
                    IP                   {default_data_type},
                    Type                 {default_data_type},
                    Continent            {default_data_type},
                    `Continent Code`     {default_data_type},
                    Country              {default_data_type},
                    `Country Code`       {default_data_type},
                    City                 {default_data_type},
                    Latitude             {default_data_type},
                    Longitude            {default_data_type},
                    `Is EU`              {default_data_type},
                    ASN                  {default_data_type},
                    Organization         {default_data_type},
                    ISP                  {default_data_type},
                    Domain               {default_data_type},
                    Timezone             {default_data_type},
                    `Flag Emoji`         {default_data_type},
                    `Flag Emoji Unicode` {default_data_type},
                    `Flag IMG`           {default_data_type}
                    "
                ))
            }

            GeoLocationConfig::DB_KEYS__IP => MYSQLValue::Str(
                "
                IP,
                Type,
                Continent,
                `Continent Code`,
                Country,
                `Country Code`,
                City,
                Latitude,
                Longitude,
                `Is EU`,
                ASN,
                Organization,
                ISP,
                Domain,
                Timezone,
                `Flag Emoji`,
                `Flag Emoji Unicode`,
                `Flag IMG`
                "
                .to_string(),
            ),

            GeoLocationConfig::DB_MARKS__IP => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),

            // commented because not to be used in rust
            // GeoLocationConfig::COUNTRY_CODES_DICT => ...
        }
    }

    pub fn get_table_name(geo_mode: &str) -> String {
        ensure_env_loaded();
        let sep = match MYSQLConfig::TABLE_NAME_SEPARATOR.value() {
            MYSQLValue::Str(s) => s,
            _ => panic!("Invalid table name separator"),
        };

        format!("{}table{}{}", Self::SLUG.value().value_string(), sep, geo_mode)
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement(geo_mode: &str) -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name(geo_mode))
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum MaliciousConfig {
    TITLE,
    SLUG,

    // DB_HEADERS_WITH_INDEXES__DOMAIN,
    DB_HEADERS__DOMAIN,
    DB_COLUMNS__DOMAIN,
    DB_KEYS__DOMAIN,
    DB_MARKS__DOMAIN,

    // DB_HEADERS_WITH_INDEXES__IP,
    DB_HEADERS__IP,
    DB_COLUMNS__IP,
    DB_KEYS__IP,
    DB_MARKS__IP,

    TABLENAMES_AND_KEYS_FOR_INDEX__PARENT,
    TABLENAMES_AND_KEYS_FOR_INDEX,
}

impl MaliciousConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            MaliciousConfig::TITLE => MYSQLValue::Str("Malicious".to_string()),
            MaliciousConfig::SLUG => MYSQLValue::Str("malicious".to_string()),  // JUMP_14

            // used by parent and children
            // commented because not to be used in rust
            // MaliciousConfig::DB_HEADERS_WITH_INDEXES__DOMAIN => ...

            MaliciousConfig::DB_HEADERS__DOMAIN => MYSQLValue::List(vec![
                "ID".to_string(),
                "Domain".to_string(),
                "Sources".to_string(),
            ]),

            MaliciousConfig::DB_COLUMNS__DOMAIN => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(indexed_column_data_type) = MYSQLConfig::INDEXED_COLUMN_DATA_TYPE.value() else {
                    panic!("Invalid config for INDEXED_COLUMN_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID      {id_data_type},
                    Domain  {indexed_column_data_type},
                    Sources {default_data_type}
                    "
                ))
            }

            MaliciousConfig::DB_KEYS__DOMAIN => MYSQLValue::Str(
                "
                Domain,
                Sources
                "
                .to_string(),
            ),

            MaliciousConfig::DB_MARKS__DOMAIN => MYSQLValue::Str("%s,%s".to_string()),

            // used by parent and children
            // commented because not to be used in rust
            // MaliciousConfig::DB_HEADERS_WITH_INDEXES__IP => ...

            MaliciousConfig::DB_HEADERS__IP => MYSQLValue::List(vec![
                "ID".to_string(),
                "IP".to_string(),
                "Sources".to_string(),
            ]),

            MaliciousConfig::DB_COLUMNS__IP => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(indexed_column_data_type) = MYSQLConfig::INDEXED_COLUMN_DATA_TYPE.value() else {
                    panic!("Invalid config for INDEXED_COLUMN_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID      {id_data_type},
                    IP      {indexed_column_data_type},
                    Sources {default_data_type}
                    "
                ))
            }

            MaliciousConfig::DB_KEYS__IP => MYSQLValue::Str(
                "
                IP,
                Sources
                "
                .to_string(),
            ),

            MaliciousConfig::DB_MARKS__IP => MYSQLValue::Str("%s,%s".to_string()),

            // used by parent
            MaliciousConfig::TABLENAMES_AND_KEYS_FOR_INDEX__PARENT => MYSQLValue::Tuple(vec![
                ("malicioustable__domain__parent".to_string(), "Domain".to_string()),
                ("malicioustable__ip__parent".to_string(),     "IP".to_string()),
            ]),

            // used by children
            MaliciousConfig::TABLENAMES_AND_KEYS_FOR_INDEX => MYSQLValue::Tuple(vec![
                ("malicioustable__domain".to_string(), "Domain".to_string()),
                ("malicioustable__ip".to_string(),     "IP".to_string()),
            ]),
        }
    }

    pub fn get_table_name(mal_mode: &str, is_parent: bool) -> String {
        ensure_env_loaded();
        let sep = match MYSQLConfig::TABLE_NAME_SEPARATOR.value() {
            MYSQLValue::Str(s) => s,
            _ => panic!("Invalid table name separator"),
        };

        let suffix = if is_parent {
            format!("{sep}parent")
        } else {
            "".to_string()
        };

        format!("{}table{}{}{}", Self::SLUG.value().value_string(), sep, mal_mode, suffix)
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement(mal_mode: &str, is_parent: bool) -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name(mal_mode, is_parent))
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum DaemonConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    EVENT_TYPES__CRITICALS,
    EVENT_TYPES__WARNINGS,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl DaemonConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            DaemonConfig::TITLE => MYSQLValue::Str("Daemon".to_string()),
            DaemonConfig::SLUG => MYSQLValue::Str("daemon".to_string()),
            DaemonConfig::FILTERBY => MYSQLValue::Str("(daemon/".to_string()),
            DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            DaemonConfig::EVENT_TYPES => MYSQLValue::List(
                _EVENT_TYPES__DAEMON.iter().map(|s| s.to_string()).collect()
            ),

            DaemonConfig::EVENT_TYPES__CRITICALS => MYSQLValue::List(
                _EVENT_TYPES__DAEMON__CRITICALS.iter().map(|s| s.to_string()).collect()
            ),

            DaemonConfig::EVENT_TYPES__WARNINGS => MYSQLValue::List(
                _EVENT_TYPES__DAEMON__WARNINGS.iter().map(|s| s.to_string()).collect()
            ),

            // commented because not to be used in rust
            // DaemonConfig::DB_HEADERS_WITH_INDEXES => ...

            DaemonConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            DaemonConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    Level        {default_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            DaemonConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Level,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            DaemonConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum DHCPConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl DHCPConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            DHCPConfig::TITLE => MYSQLValue::Str("DHCP".to_string()),
            DHCPConfig::SLUG => MYSQLValue::Str("dhcp".to_string()),
            DHCPConfig::FILTERBY => MYSQLValue::Str("[dhcp]".to_string()),
            DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            DHCPConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(syslog/info)".to_string(),
                "(user/notice)".to_string(),
            ]),

            // commented because not to be used in rust
            // DHCPConfig::DB_HEADERS_WITH_INDEXES => ...

            DHCPConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Event ID".to_string(),
                "Description".to_string(),
                "Source IP".to_string(),
                "Host Name".to_string(),
                "MAC Address".to_string(),
                "User Name".to_string(),
                "TransactionID".to_string(),
                "QResult".to_string(),
                "Probationtime".to_string(),
                "CorrelationID".to_string(),
                "Dhcid".to_string(),
                "VendorClass(Hex)".to_string(),
                "VendorClass(ASCII)".to_string(),
                "UserClass(Hex)".to_string(),
                "UserClass(ASCII)".to_string(),
                "RelayAgentInformation".to_string(),
                "DnsRegError".to_string(),
            ]),

            DHCPConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                    {id_data_type},
                    Date                  {date_data_type},
                    Time                  {time_data_type},
                    `Event ID`            {default_data_type},
                    Description           {default_data_type},
                    `Source IP`           {default_data_type},
                    `Host Name`           {default_data_type},
                    `MAC Address`         {default_data_type},
                    `User Name`           {default_data_type},
                    TransactionID         {default_data_type},
                    QResult               {default_data_type},
                    Probationtime         {default_data_type},
                    CorrelationID         {default_data_type},
                    Dhcid                 {default_data_type},
                    `VendorClass(Hex)`    {default_data_type},
                    `VendorClass(ASCII)`  {default_data_type},
                    `UserClass(Hex)`      {default_data_type},
                    `UserClass(ASCII)`    {default_data_type},
                    RelayAgentInformation {default_data_type},
                    DnsRegError           {default_data_type}
                    "
                ))
            }

            DHCPConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Event ID`,
                Description,
                `Source IP`,
                `Host Name`,
                `MAC Address`,
                `User Name`,
                TransactionID,
                QResult,
                Probationtime,
                CorrelationID,
                Dhcid,
                `VendorClass(Hex)`,
                `VendorClass(ASCII)`,
                `UserClass(Hex)`,
                `UserClass(ASCII)`,
                RelayAgentInformation,
                DnsRegError
                "
                .to_string(),
            ),

            DHCPConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum DNSConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl DNSConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            DNSConfig::TITLE => MYSQLValue::Str("DNS".to_string()),
            DNSConfig::SLUG => MYSQLValue::Str("dns".to_string()),
            DNSConfig::FILTERBY => MYSQLValue::Str("[dns]".to_string()),
            DNSConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            DNSConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(syslog/info)".to_string(),
                "(user/notice)".to_string(),
            ]),

            // commented because not to be used in rust
            // DNSConfig::DB_HEADERS_WITH_INDEXES => ...

            DNSConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Thread ID".to_string(),
                "Context".to_string(),
                "Internal Packet Identifier".to_string(),
                "UDP/TCP Indicator".to_string(),
                "Send/Receive Indicator".to_string(),
                "Source IP".to_string(),
                "Xid (hex)".to_string(),
                "Query/Response".to_string(),
                "Opcode".to_string(),
                "Flags (hex)".to_string(),
                "Flags (char codes)".to_string(),
                "ResponseCode".to_string(),
                "Question Type".to_string(),
                "Question Name".to_string(),
            ]),

            DNSConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                           {id_data_type},
                    Date                         {date_data_type},
                    Time                         {time_data_type},
                    `Thread ID`                  {default_data_type},
                    Context                      {default_data_type},
                    `Internal Packet Identifier` {default_data_type},
                    `UDP/TCP Indicator`          {default_data_type},
                    `Send/Receive Indicator`     {default_data_type},
                    `Source IP`                  {default_data_type},
                    `Xid (hex)`                  {default_data_type},
                    `Query/Response`             {default_data_type},
                    Opcode                       {default_data_type},
                    `Flags (hex)`                {default_data_type},
                    `Flags (char codes)`         {default_data_type},
                    ResponseCode                 {default_data_type},
                    `Question Type`              {default_data_type},
                    `Question Name`              {default_data_type}
                    "
                ))
            }

            DNSConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Thread ID`,
                Context,
                `Internal Packet Identifier`,
                `UDP/TCP Indicator`,
                `Send/Receive Indicator`,
                `Source IP`,
                `Xid (hex)`,
                `Query/Response`,
                Opcode,
                `Flags (hex)`,
                `Flags (char codes)`,
                ResponseCode,
                `Question Type`,
                `Question Name`
                "
                .to_string(),
            ),

            DNSConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum FilterLogConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl FilterLogConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            FilterLogConfig::TITLE => MYSQLValue::Str("FilterLog".to_string()),
            FilterLogConfig::SLUG => MYSQLValue::Str("filterlog".to_string()),
            FilterLogConfig::FILTERBY => MYSQLValue::Str("[filterlog]".to_string()),
            FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            FilterLogConfig::EVENT_TYPES => MYSQLValue::List(vec![]),

            // commented because not to be used in rust
            // FilterLogConfig::DB_HEADERS_WITH_INDEXES => ...

            FilterLogConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Protocol Name".to_string(),
                "Source IP".to_string(),
                "Destination IP".to_string(),
                "Source Port".to_string(),
                "Destination Port".to_string(),
                "Tracking ID".to_string(),
                "Real Interface".to_string(),
                "Reason".to_string(),
                "Action".to_string(),
                "Direction".to_string(),
            ]),

            FilterLogConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                 {id_data_type},
                    Date               {date_data_type},
                    Time               {time_data_type},
                    `Protocol Name`    {default_data_type},
                    `Source IP`        {default_data_type},
                    `Destination IP`   {default_data_type},
                    `Source Port`      {default_data_type},
                    `Destination Port` {default_data_type},
                    `Tracking ID`      {default_data_type},
                    `Real Interface`   {default_data_type},
                    Reason             {default_data_type},
                    Action             {default_data_type},
                    Direction          {default_data_type}
                    "
                ))
            }

            FilterLogConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Protocol Name`,
                `Source IP`,
                `Destination IP`,
                `Source Port`,
                `Destination Port`,
                `Tracking ID`,
                `Real Interface`,
                Reason,
                Action,
                Direction
                "
                .to_string(),
            ),

            FilterLogConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum RouterConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl RouterConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            RouterConfig::TITLE => MYSQLValue::Str("Router".to_string()),
            RouterConfig::SLUG => MYSQLValue::Str("router".to_string()),
            RouterConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            RouterConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            RouterConfig::EVENT_TYPES => MYSQLValue::List(
                EVENT_TYPES.iter().map(|s| s.to_string()).collect()
            ),

            // commented because not to be used in rust
            // RouterConfig::DB_HEADERS_WITH_INDEXES => ...

            RouterConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            RouterConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    Level        {default_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            RouterConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Level,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            RouterConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum RouterBoardConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl RouterBoardConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            RouterBoardConfig::TITLE => MYSQLValue::Str("RouterBoard".to_string()),
            RouterBoardConfig::SLUG => MYSQLValue::Str("routerboard".to_string()),
            RouterBoardConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            RouterBoardConfig::EVENT_TYPES => MYSQLValue::List(
                EVENT_TYPES.iter().map(|s| s.to_string()).collect()
            ),

            // commented because not to be used in rust
            // RouterBoardConfig::DB_HEADERS_WITH_INDEXES => ...

            RouterBoardConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            RouterBoardConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    Level        {default_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            RouterBoardConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Level,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            RouterBoardConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum SnortConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,

    TABLENAMES_AND_KEYS_FOR_INDEX,

    CLASSIFICATIONS_DICT,
    CLASSIFICATIONS__CRITICALS,
    CLASSIFICATIONS__WARNINGS,
    CLASSIFICATIONS__LOWS,
    CLASSIFICATIONS__VERY_LOWS,
}

impl SnortConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            SnortConfig::TITLE => MYSQLValue::Str("Snort".to_string()),
            SnortConfig::SLUG => MYSQLValue::Str("snort".to_string()),
            SnortConfig::FILTERBY => MYSQLValue::Str("[snort]".to_string()),
            SnortConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            SnortConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(auth/alert)".to_string(),
            ]),

            // commented because not to be used in rust
            // SnortConfig::DB_HEADERS_WITH_INDEXES => ...

            SnortConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "GID:SID".to_string(),
                "Description".to_string(),
                "Classification".to_string(),
                "Priority".to_string(),
                "Protocol".to_string(),
                "Source IP".to_string(),
                "Source Port".to_string(),
                "Destination IP".to_string(),
                "Destination Port".to_string(),
            ]),

            SnortConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(indexed_column_data_type) = MYSQLConfig::INDEXED_COLUMN_DATA_TYPE.value() else {
                    panic!("Invalid config for INDEXED_COLUMN_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                 {id_data_type},
                    Date               {date_data_type},
                    Time               {time_data_type},
                    `GID:SID`          {default_data_type},
                    Description        {default_data_type},
                    Classification     {indexed_column_data_type},
                    Priority           {default_data_type},
                    Protocol           {default_data_type},
                    `Source IP`        {default_data_type},
                    `Source Port`      {default_data_type},
                    `Destination IP`   {default_data_type},
                    `Destination Port` {default_data_type}
                    "
                ))
            }

            SnortConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `GID:SID`,
                Description,
                Classification,
                Priority,
                Protocol,
                `Source IP`,
                `Source Port`,
                `Destination IP`,
                `Destination Port`
                "
                .to_string(),
            ),

            SnortConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),

            SnortConfig::TABLENAMES_AND_KEYS_FOR_INDEX => MYSQLValue::Tuple(vec![
                ("snorttable".to_string(), "Classification".to_string()),
            ]),

            SnortConfig::CLASSIFICATIONS_DICT => {
                // convert the imported CLASSIFICATIONS_DICT directly
                let mut dict = HashMap::new();
                for (key, value) in CLASSIFICATIONS_DICT.iter() {
                    dict.insert(key.to_string(), value);
                }
                MYSQLValue::ClassificationDict(dict)
            },

            SnortConfig::CLASSIFICATIONS__CRITICALS => {
                // convert the imported CLASSIFICATIONS__CRITICALS to Vec<String>
                let mut criticals: Vec<String> = CLASSIFICATIONS__CRITICALS.iter().map(|s| s.to_string()).collect();
                criticals.sort();
                MYSQLValue::List(criticals)
            },

            SnortConfig::CLASSIFICATIONS__WARNINGS => {
                // convert the imported CLASSIFICATIONS__WARNINGS to Vec<String>
                let mut warnings: Vec<String> = CLASSIFICATIONS__WARNINGS.iter().map(|s| s.to_string()).collect();
                warnings.sort();
                MYSQLValue::List(warnings)
            },

            SnortConfig::CLASSIFICATIONS__LOWS => {
                // convert the imported CLASSIFICATIONS__LOWS to Vec<String>
                let mut lows: Vec<String> = CLASSIFICATIONS__LOWS.iter().map(|s| s.to_string()).collect();
                lows.sort();
                MYSQLValue::List(lows)
            },

            SnortConfig::CLASSIFICATIONS__VERY_LOWS => {
                // convert the imported CLASSIFICATIONS__VERY_LOWS to Vec<String>
                let mut very_lows: Vec<String> = CLASSIFICATIONS__VERY_LOWS.iter().map(|s| s.to_string()).collect();
                very_lows.sort();
                MYSQLValue::List(very_lows)
            },
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum SquidConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl SquidConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            SquidConfig::TITLE => MYSQLValue::Str("Squid".to_string()),
            SquidConfig::SLUG => MYSQLValue::Str("squid".to_string()),
            SquidConfig::FILTERBY => MYSQLValue::Str("[(squid-1)]".to_string()),
            SquidConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            SquidConfig::EVENT_TYPES => MYSQLValue::List(vec![]),

            // https://community.splunk.com/t5/Getting-Data-In/How-to-to-extract-fields-from-Squid-logs-to-Splunk-from-PFsense/m-p/152579
            // https://kifarunix.com/create-squid-logs-extractors-on-graylog-server/

            // commented because not to be used in rust
            // SquidConfig::DB_HEADERS_WITH_INDEXES => ...

            SquidConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Duration".to_string(),
                "Source IP".to_string(),
                "Request Status".to_string(),
                "Status Code".to_string(),
                "Transfer".to_string(),
                "HTTP Method".to_string(),
                "URL".to_string(),
                "Client Identity".to_string(),
                "Peer Code".to_string(),
                "Destination IP".to_string(),
                "Content Type".to_string(),
            ]),

            SquidConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                 {id_data_type},
                    Date               {date_data_type},
                    Time               {time_data_type},
                    Duration           {default_data_type},
                    `Source IP`        {default_data_type},
                    `Request Status`   {default_data_type},
                    `Status Code`      {default_data_type},
                    Transfer           {default_data_type},
                    `HTTP Method`      {default_data_type},
                    URL                {default_data_type},
                    `Client Identity`  {default_data_type},
                    `Peer Code`        {default_data_type},
                    `Destination IP`   {default_data_type},
                    `Content Type`     {default_data_type}
                    "
                ))
            }

            SquidConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Duration,
                `Source IP`,
                `Request Status`,
                `Status Code`,
                Transfer,
                `HTTP Method`,
                URL,
                `Client Identity`,
                `Peer Code`,
                `Destination IP`,
                `Content Type`
                "
                .to_string(),
            ),

            SquidConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum SwitchConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl SwitchConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            SwitchConfig::TITLE => MYSQLValue::Str("Switch".to_string()),
            SwitchConfig::SLUG => MYSQLValue::Str("switch".to_string()),
            SwitchConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            SwitchConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            SwitchConfig::EVENT_TYPES => MYSQLValue::List(
                EVENT_TYPES.iter().map(|s| s.to_string()).collect()
            ),

            // commented because not to be used in rust
            // SwitchConfig::DB_HEADERS_WITH_INDEXES => ...

            SwitchConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            SwitchConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    Level        {default_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            SwitchConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Level,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            SwitchConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum UserAuditConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl UserAuditConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            UserAuditConfig::TITLE => MYSQLValue::Str("User Audit".to_string()),
            UserAuditConfig::SLUG => MYSQLValue::Str("useraudit".to_string()),
            UserAuditConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            UserAuditConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            UserAuditConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(daemon/err)".to_string(),
                "(auth/emerg)".to_string(),
            ]),

            // commented because not to be used in rust
            // UserAuditConfig::DB_HEADERS_WITH_INDEXES => ...

            UserAuditConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            UserAuditConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            UserAuditConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            UserAuditConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum UserNoticeConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl UserNoticeConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            UserNoticeConfig::TITLE => MYSQLValue::Str("User Notice".to_string()),
            UserNoticeConfig::SLUG => MYSQLValue::Str("usernotice".to_string()),
            UserNoticeConfig::FILTERBY => MYSQLValue::Str("[openvpn]".to_string()),
            UserNoticeConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(true),

            UserNoticeConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(user/notice)".to_string(),
            ]),

            // commented because not to be used in rust
            // UserNoticeConfig::DB_HEADERS_WITH_INDEXES => ...

            UserNoticeConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Server".to_string(),
                "User".to_string(),
                "Destination IP".to_string(),
                "Port".to_string(),
                "Status".to_string(),
                
            ]),

            UserNoticeConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID               {id_data_type},
                    Date             {date_data_type},
                    Time             {time_data_type},
                    Server           {default_data_type},
                    User             {default_data_type},
                    `Destination IP` {default_data_type},
                    Port             {default_data_type},
                    Status           {default_data_type}
                    "
                ))
            }

            UserNoticeConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Server,
                User,
                `Destination IP`,
                Port,
                Status
                "
                .to_string(),
            ),

            UserNoticeConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum UserWarningConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,

    PACKET_LOSS_DB_HEADERS,
}

impl UserWarningConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            UserWarningConfig::TITLE => MYSQLValue::Str("User Warning".to_string()),
            UserWarningConfig::SLUG => MYSQLValue::Str("userwarning".to_string()),
            UserWarningConfig::FILTERBY => MYSQLValue::Str("(user/warning)".to_string()),
            UserWarningConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            UserWarningConfig::EVENT_TYPES => MYSQLValue::List(vec![
                "(user/warning)".to_string(),
            ]),

            // commented because not to be used in rust
            // UserWarningConfig::DB_HEADERS_WITH_INDEXES => ...

            UserWarningConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            UserWarningConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            UserWarningConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            UserWarningConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s".to_string()),

            UserWarningConfig::PACKET_LOSS_DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Gateway".to_string(),
                "Count".to_string(),
                "Minimum".to_string(),
                "Maximum".to_string(),
                "Average".to_string(),
            ]),

        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum VMwareConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl VMwareConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            VMwareConfig::TITLE => MYSQLValue::Str("VMware".to_string()),
            VMwareConfig::SLUG => MYSQLValue::Str("vmware".to_string()),
            VMwareConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            VMwareConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            VMwareConfig::EVENT_TYPES => MYSQLValue::List(vec![]),

            // commented because not to be used in rust
            // VMwareConfig::DB_HEADERS_WITH_INDEXES => ...

            VMwareConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Level".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
            ]),

            VMwareConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID           {id_data_type},
                    Date         {date_data_type},
                    Time         {time_data_type},
                    Level        {default_data_type},
                    `Alert Type` {default_data_type},
                    Message      {default_data_type}
                    "
                ))
            }

            VMwareConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Level,
                `Alert Type`,
                Message
                "
                .to_string(),
            ),

            VMwareConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum VPNServerConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,
}

impl VPNServerConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            VPNServerConfig::TITLE => MYSQLValue::Str("VPN Server".to_string()),
            VPNServerConfig::SLUG => MYSQLValue::Str("vpnserver".to_string()),
            VPNServerConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            VPNServerConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            VPNServerConfig::EVENT_TYPES => MYSQLValue::List(vec![]),

            // commented because not to be used in rust
            // VPNServerConfig::DB_HEADERS_WITH_INDEXES => ...

            VPNServerConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Domain".to_string(),
                "Username".to_string(),
                "Port".to_string(),
                "Active For".to_string(),
                "Sent".to_string(),
                "Received".to_string(),
                "Source IP".to_string(),
                "Destination IPs".to_string(),                
            ]),

            VPNServerConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                {id_data_type},
                    Date              {date_data_type},
                    Time              {time_data_type},
                    Domain            {default_data_type},
                    Username          {default_data_type},
                    Port              {default_data_type},
                    `Active For`      {default_data_type},
                    Sent              {default_data_type},
                    Received          {default_data_type},
                    `Source IP`       {default_data_type},
                    `Destination IPs` {default_data_type}
                    "
                ))
            }

            VPNServerConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                Domain,
                Username,
                Port,
                `Active For`,
                Sent,
                Received,
                `Source IP`,
                `Destination IPs`
                "
                .to_string(),
            ),

            VPNServerConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),
        }
    }

    pub fn get_table_name() -> String {
        format!("{}table", Self::SLUG.value().value_string())
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement() -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name())
    // }
}


#[allow(non_camel_case_types)]
#[derive(Debug)]
pub enum WindowsServerConfig {
    TITLE,
    SLUG,
    FILTERBY,
    FILTERBY_IS_IN_ALERT_TYPE,
    EVENT_TYPES,
    // DB_HEADERS_WITH_INDEXES,
    DB_HEADERS,
    DB_COLUMNS,
    DB_KEYS,
    DB_MARKS,

    EVENT_IDS_AND_POTENTIAL_CRITICALITIES,
    // EVENT_IDS__HIGH,
    // EVENT_IDS__MEDIUM,
    // HIGH_POTENTIAL_CRITICALITIES,
    // MEDIUM_POTENTIAL_CRITICALITIES,
    EVENT_IDS__ACCOUNT_LOGON,
    EVENT_IDS__ACCOUNT_MANAGEMENT,
    EVENT_IDS__DETAILED_TRACKING,
    EVENT_IDS__DS_ACCESS,
    EVENT_IDS__LOGONLOGOFF,
    EVENT_IDS__OBJECT_ACCESS,
    EVENT_IDS__POLICY_CHANGE,
    EVENT_IDS__PRIVILEGE_USE,
    EVENT_IDS__SYSTEM,
    EVENT_IDS__MISCELLANEOUS,
}

impl WindowsServerConfig {
    pub fn value(&self) -> MYSQLValue {
        ensure_env_loaded();

        match self {
            WindowsServerConfig::TITLE => MYSQLValue::Str("Windows Server".to_string()),
            WindowsServerConfig::SLUG => MYSQLValue::Str("windowsserver".to_string()),
            WindowsServerConfig::FILTERBY => MYSQLValue::Str("".to_string()),
            WindowsServerConfig::FILTERBY_IS_IN_ALERT_TYPE => MYSQLValue::Bool(false),

            WindowsServerConfig::EVENT_TYPES => MYSQLValue::List(vec![]),

            // commented because not to be used in rust
            // WindowsServerConfig::DB_HEADERS_WITH_INDEXES => ...

            WindowsServerConfig::DB_HEADERS => MYSQLValue::List(vec![
                "ID".to_string(),
                "Date".to_string(),
                "Time".to_string(),
                "Alert Type".to_string(),
                "Message".to_string(),
                "Event ID".to_string(),
                "Category".to_string(),
                "Potential Criticality".to_string(),
                "Account Name".to_string(),
                "Account Domain".to_string(),
                "Source Workstation".to_string(),
            ]),

            WindowsServerConfig::DB_COLUMNS => {
                let MYSQLValue::Str(id_data_type) = MYSQLConfig::ID_DATA_TYPE.value() else {
                    panic!("Invalid config for ID_DATA_TYPE");
                };
                let MYSQLValue::Str(date_data_type) = MYSQLConfig::DATE_DATA_TYPE.value() else {
                    panic!("Invalid config for DATE_DATA_TYPE");
                };
                let MYSQLValue::Str(time_data_type) = MYSQLConfig::TIME_DATA_TYPE.value() else {
                    panic!("Invalid config for TIME_DATA_TYPE");
                };
                let MYSQLValue::Str(default_data_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else {
                    panic!("Invalid config for DEFAULT_DATA_TYPE");
                };

                MYSQLValue::Str(format!(
                    "
                    ID                      {id_data_type},
                    Date                    {date_data_type},
                    Time                    {time_data_type},
                    `Alert Type`            {default_data_type},
                    Message                 {default_data_type},
                    `Event ID`              {default_data_type},
                    Category                {default_data_type},
                    `Potential Criticality` {default_data_type},
                    `Account Name`          {default_data_type},
                    `Account Domain`        {default_data_type},
                    `Source Workstation`    {default_data_type}
                    "
                ))
            }

            WindowsServerConfig::DB_KEYS => MYSQLValue::Str(
                "
                Date,
                Time,
                `Alert Type`,
                Message,
                `Event ID`,
                Category,
                `Potential Criticality`,
                `Account Name`,
                `Account Domain`,
                `Source Workstation`
                "
                .to_string(),
            ),

            WindowsServerConfig::DB_MARKS => MYSQLValue::Str("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s".to_string()),

            WindowsServerConfig::EVENT_IDS_AND_POTENTIAL_CRITICALITIES => {
                let mut dict = HashMap::new();
                for (event_id, info) in WINDOWS_SERVER_AUDIT_EVENTS.iter() {
                    dict.insert(event_id.to_string(), info.potential_criticality.to_string());
                }
                MYSQLValue::Dict(dict)
            }
            // {
            //     '4745': 'Low',
            //     '4746': 'Medium',
            //     ...
            // }

            // commented because not to be used in rust
            // used to highlight warning/critical rows of html tables
            // WindowsServerConfig::EVENT_IDS__HIGH ...
            // WindowsServerConfig::EVENT_IDS__MEDIUM...
            // WindowsServerConfig::HIGH_POTENTIAL_CRITICALITIES...
            // WindowsServerConfig::MEDIUM_POTENTIAL_CRITICALITIES...

            WindowsServerConfig::EVENT_IDS__ACCOUNT_LOGON => {
                let account_logon_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Account Logon")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(account_logon_ids)
            }
            // ['4608', '4609', ...]

            WindowsServerConfig::EVENT_IDS__ACCOUNT_MANAGEMENT => {
                let account_management_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Account Management")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(account_management_ids)
            }

            WindowsServerConfig::EVENT_IDS__DETAILED_TRACKING => {
                let detailed_tracking_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Detailed Tracking")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(detailed_tracking_ids)
            }

            WindowsServerConfig::EVENT_IDS__DS_ACCESS => {
                let ds_access_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "DS Access")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(ds_access_ids)
            }

            WindowsServerConfig::EVENT_IDS__LOGONLOGOFF => {
                let logonlogoff_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Logon/Logoff")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(logonlogoff_ids)
            }

            WindowsServerConfig::EVENT_IDS__OBJECT_ACCESS => {
                let object_access_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Object Access")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(object_access_ids)
            }

            WindowsServerConfig::EVENT_IDS__POLICY_CHANGE => {
                let policy_change_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Policy Change")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(policy_change_ids)
            }

            WindowsServerConfig::EVENT_IDS__PRIVILEGE_USE => {
                let privilege_use_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Privilege Use")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(privilege_use_ids)
            }

            WindowsServerConfig::EVENT_IDS__SYSTEM => {
                let system_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "System")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(system_ids)
            }

            WindowsServerConfig::EVENT_IDS__MISCELLANEOUS => {
                let miscellaneous_ids: Vec<String> = WINDOWS_SERVER_AUDIT_EVENTS
                    .iter()
                    .filter(|(_, info)| info.category == "Miscellaneous")
                    .map(|(event_id, _)| event_id.to_string())
                    .collect();
                MYSQLValue::List(miscellaneous_ids)
            }
        }
    }

    pub fn get_table_name(category: Option<&str>) -> String {
        ensure_env_loaded();
        let sep = match MYSQLConfig::TABLE_NAME_SEPARATOR.value() {
            MYSQLValue::Str(s) => s,
            _ => panic!("Invalid table name separator"),
        };

        let suffix = if let Some(category) = category {
            if category.is_empty() {
                "".to_string()
            } else {
                format!("{}{}", sep, category)
            }
        } else {
            "".to_string()
        };

        format!("{}table{}", Self::SLUG.value().value_string(), suffix)
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR")
                              .unwrap_or_else(|_| panic!("Missing LOGS_PARSED_DIR in .env"));
        format!("{}/{}", logs_parsed_dir, Self::SLUG.value().value_string())
    }

    // commented because not to be used in rust
    // pub fn get_select_statement(category: Option<&str>) -> String {
    //     format!("SELECT * FROM {}", Self::get_table_name(category))
    // }

    pub fn get_category_from_event_id(event_id: &str) -> String {
        WINDOWS_SERVER_AUDIT_EVENTS
            .get(event_id)
            .map(|info| info.category.to_string())
            .unwrap_or_else(|| "".to_string())
    }
}

// -------------------------------
// *Parser

pub trait Parser {
    // commented because not to be used in rust
    // fn to_string(&self) -> String {
    // }

    fn no_of_rows(&self) -> usize;

    fn truncate_rows(&mut self);
}


// commented because not to be used in rust
// GeoLocationParser


// commented because not to be used in rust
// MaliciousParser


#[derive(Debug, Clone)]
pub struct DaemonParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    // for levelcounttable
    pub daemon_errors_count: i32,
    pub daemon_warnings_count: i32,
}

impl DaemonParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),

            // for levelcounttable
            daemon_errors_count: 0,
            daemon_warnings_count: 0,
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        // for levelcounttable
        self.daemon_errors_count = 0;
        self.daemon_warnings_count = 0;
    }
}

impl Parser for DaemonParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct DHCPParser {
    pub slug: String,
    pub ymd: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub descriptions_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,
    pub host_names_and_counts: HashMap<String, i32>,
    pub mac_addresses_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,

    pub ips_macs: HashMap<String, String>,
}

impl DHCPParser {
    pub fn new(slug: String, ymd: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            rows: Vec::new(),

            times_and_counts,
            descriptions_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),
            host_names_and_counts: HashMap::new(),
            mac_addresses_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),

            ips_macs: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.descriptions_and_counts.clear();
        self.source_ips_and_counts.clear();
        self.host_names_and_counts.clear();
        self.mac_addresses_and_counts.clear();

        self.milliseconds_and_counts.clear();

        self.ips_macs.clear();
    }
}

impl Parser for DHCPParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct DNSParser {
    pub slug: String,
    pub ymd: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub udp_tcp_indicators_and_counts: HashMap<String, i32>,
    pub send_receive_indicators_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,
    pub responsecodes_and_counts: HashMap<String, i32>,
    pub question_types_and_counts: HashMap<String, i32>,
    pub question_names_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,
    pub destination_ips_counts: HashMap<String, i32>,

    // __FIXME__ in python, these are defaultdict, not normal dict
    pub ips_domains: HashMap<String, Vec<String>>,
    pub domains_ips: HashMap<String, Vec<String>>,

    pub malicious_ips_and_counts: HashMap<String, i32>,
    pub malicious_domains_and_counts: HashMap<String, i32>,

    // values are dictionaries, not string
    pub ips_domains_counts: HashMap<String, HashMap<String, i32>>,
    pub domains_ips_counts: HashMap<String, HashMap<String, i32>>,
}

impl DNSParser {
    pub fn new(slug: String, ymd: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            rows: Vec::new(),

            times_and_counts,
            udp_tcp_indicators_and_counts: HashMap::new(),
            send_receive_indicators_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),
            responsecodes_and_counts: HashMap::new(),
            question_types_and_counts: HashMap::new(),
            question_names_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
            destination_ips_counts: HashMap::new(),

            ips_domains: HashMap::new(),
            domains_ips: HashMap::new(),

            malicious_ips_and_counts: HashMap::new(),
            malicious_domains_and_counts: HashMap::new(),

            ips_domains_counts: HashMap::new(),
            domains_ips_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.udp_tcp_indicators_and_counts.clear();
        self.send_receive_indicators_and_counts.clear();
        self.source_ips_and_counts.clear();
        self.responsecodes_and_counts.clear();
        self.question_types_and_counts.clear();
        self.question_names_and_counts.clear();

        self.milliseconds_and_counts.clear();
        self.destination_ips_counts.clear();

        self.ips_domains.clear();
        self.domains_ips.clear();

        self.malicious_ips_and_counts.clear();
        self.malicious_domains_and_counts.clear();

        self.ips_domains_counts.clear();
        self.domains_ips_counts.clear();
    }
}

impl Parser for DNSParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct FilterLogParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub tracking_ids_and_counts: HashMap<String, i32>,
    pub real_interfaces_and_counts: HashMap<String, i32>,
    pub reasons_and_counts: HashMap<String, i32>,
    pub actions_and_counts: HashMap<String, i32>,
    pub directions_and_counts: HashMap<String, i32>,
    pub protocol_names_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,
    pub source_ports_and_counts: HashMap<String, i32>,
    pub destination_ips_and_counts: HashMap<String, i32>,
    pub destination_ports_and_counts: HashMap<String, i32>,

    // __FIXME__ in python, these are defaultdict, not normal dict
    pub src_ips_and_dest_ips: HashMap<String, Vec<String>>,

    pub milliseconds_and_counts: HashMap<String, i32>,
}

impl FilterLogParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),

            times_and_counts,
            tracking_ids_and_counts: HashMap::new(),
            real_interfaces_and_counts: HashMap::new(),
            reasons_and_counts: HashMap::new(),
            actions_and_counts: HashMap::new(),
            directions_and_counts: HashMap::new(),
            protocol_names_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),
            source_ports_and_counts: HashMap::new(),
            destination_ips_and_counts: HashMap::new(),
            destination_ports_and_counts: HashMap::new(),

            src_ips_and_dest_ips: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.tracking_ids_and_counts.clear();
        self.real_interfaces_and_counts.clear();
        self.reasons_and_counts.clear();
        self.actions_and_counts.clear();
        self.directions_and_counts.clear();
        self.protocol_names_and_counts.clear();
        self.source_ips_and_counts.clear();
        self.source_ports_and_counts.clear();
        self.destination_ips_and_counts.clear();
        self.destination_ports_and_counts.clear();

        self.src_ips_and_dest_ips.clear();

        self.milliseconds_and_counts.clear();
    }
}

impl Parser for FilterLogParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct RouterParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl RouterParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();
    }
}

impl Parser for RouterParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct RouterBoardParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl RouterBoardParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();
    }
}

impl Parser for RouterBoardParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct SnortParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub gidsids_and_counts: HashMap<String, i32>,
    pub descriptions_and_counts: HashMap<String, i32>,
    pub classifications_and_counts: HashMap<String, i32>,
    pub priorities_and_counts: HashMap<String, i32>,
    pub protocols_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,
    pub source_ports_and_counts: HashMap<String, i32>,
    pub destination_ips_and_counts: HashMap<String, i32>,
    pub destination_ports_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,
    pub levels_and_counts: HashMap<String, i32>,
}

impl SnortParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),

            times_and_counts,
            gidsids_and_counts: HashMap::new(),
            descriptions_and_counts: HashMap::new(),
            classifications_and_counts: HashMap::new(),
            priorities_and_counts: HashMap::new(),
            protocols_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),
            source_ports_and_counts: HashMap::new(),
            destination_ips_and_counts: HashMap::new(),
            destination_ports_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
            levels_and_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.gidsids_and_counts.clear();
        self.descriptions_and_counts.clear();
        self.classifications_and_counts.clear();
        self.priorities_and_counts.clear();
        self.protocols_and_counts.clear();
        self.source_ips_and_counts.clear();
        self.source_ports_and_counts.clear();
        self.destination_ips_and_counts.clear();
        self.destination_ports_and_counts.clear();

        self.milliseconds_and_counts.clear();
        self.levels_and_counts.clear();
    }
}

impl Parser for SnortParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct SquidParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub durations_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,
    pub request_statuses_and_counts: HashMap<String, i32>,
    pub status_codes_and_counts: HashMap<String, i32>,
    pub transfers_and_counts: HashMap<String, i32>,
    pub http_methods_and_counts: HashMap<String, i32>,
    pub urls_and_counts: HashMap<String, i32>,
    pub client_identities_and_counts: HashMap<String, i32>,
    pub peer_codes_and_counts: HashMap<String, i32>,
    pub destination_ips_and_counts: HashMap<String, i32>,
    pub content_types_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,

    // __FIXME__ in python, these are defaultdict, not normal dict
    pub ips_domains: HashMap<String, Vec<String>>,
    pub domains_ips: HashMap<String, Vec<String>>,

    // __FIXME__ in python, these are defaultdict, not normal dict
    pub ips_transfers: HashMap<String, Vec<String>>,
    pub ips_durations: HashMap<String, Vec<String>>,
    // pub urls_transfers: HashMap<String, Vec<String>>,
    // pub urls_durations: HashMap<String, Vec<String>>,

    // values are dictionaries, not string
    pub ips_domains_counts: HashMap<String, HashMap<String, i32>>,
    pub domains_ips_counts: HashMap<String, HashMap<String, i32>>,
}

impl SquidParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),

            times_and_counts,
            durations_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),
            request_statuses_and_counts: HashMap::new(),
            status_codes_and_counts: HashMap::new(),
            transfers_and_counts: HashMap::new(),
            http_methods_and_counts: HashMap::new(),
            urls_and_counts: HashMap::new(),
            client_identities_and_counts: HashMap::new(),
            peer_codes_and_counts: HashMap::new(),
            destination_ips_and_counts: HashMap::new(),
            content_types_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),

            ips_domains: HashMap::new(),
            domains_ips: HashMap::new(),

            ips_transfers: HashMap::new(),
            ips_durations: HashMap::new(),
            // urls_transfers: HashMap::new(),
            // urls_durations: HashMap::new(),

            ips_domains_counts: HashMap::new(),
            domains_ips_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.durations_and_counts.clear();
        self.source_ips_and_counts.clear();
        self.request_statuses_and_counts.clear();
        self.status_codes_and_counts.clear();
        self.transfers_and_counts.clear();
        self.http_methods_and_counts.clear();
        self.urls_and_counts.clear();
        self.client_identities_and_counts.clear();
        self.peer_codes_and_counts.clear();
        self.destination_ips_and_counts.clear();
        self.content_types_and_counts.clear();

        self.milliseconds_and_counts.clear();

        self.ips_domains.clear();
        self.domains_ips.clear();

        self.ips_transfers.clear();
        self.ips_durations.clear();

        self.ips_domains_counts.clear();
        self.domains_ips_counts.clear();
    }
}

impl Parser for SquidParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct SwitchParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl SwitchParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();
    }
}

impl Parser for SwitchParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct UserAuditParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl UserAuditParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();
    }
}

impl Parser for UserAuditParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct UserNoticeParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub servers_and_counts: HashMap<String, i32>,
    pub users_and_counts: HashMap<String, i32>,
    pub destinationips_and_counts: HashMap<String, i32>,
    pub ports_and_counts: HashMap<String, i32>,
    pub statuses_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,
}

impl UserNoticeParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),

            times_and_counts,
            servers_and_counts: HashMap::new(),
            users_and_counts: HashMap::new(),
            destinationips_and_counts: HashMap::new(),
            ports_and_counts: HashMap::new(),
            statuses_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.servers_and_counts.clear();
        self.users_and_counts.clear();
        self.destinationips_and_counts.clear();
        self.ports_and_counts.clear();
        self.statuses_and_counts.clear();

        self.milliseconds_and_counts.clear();
    }
}

impl Parser for UserNoticeParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct UserWarningParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,

    // values are dictionaries, not string
    pub gateways_dict: HashMap<String, HashMap<String, i32>>,

    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    // for packetlosstable
    pub gateway_rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl UserWarningParser {
    pub fn new(slug: String, ymd: String, object_name: String, gateways_dict: HashMap<String, HashMap<String, i32>>) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            gateways_dict,
            rows: Vec::new(),

            // for packetlosstable
            gateway_rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.gateways_dict.clear();

        // for packetlosstable
        self.gateway_rows.clear();
    }
}

impl Parser for UserWarningParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct VMwareParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items
}

impl VMwareParser {
    pub fn new(slug: String, ymd: String, object_name: String) -> Self {
        Self {
            slug,
            ymd,
            object_name,
            rows: Vec::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();
    }
}

impl Parser for VMwareParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct WindowsServerParser {
    pub slug: String,
    pub ymd: String,
    pub object_name: String,
    pub category: Option<String>,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub eventids_and_counts: HashMap<String, i32>,
    pub categories_and_counts: HashMap<String, i32>,
    pub potentialcriticalities_and_counts: HashMap<String, i32>,
    pub accountnames_and_counts: HashMap<String, i32>,
    pub accountdomains_and_counts: HashMap<String, i32>,
    pub sourceworkstations_and_counts: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,
}

impl WindowsServerParser {
    pub fn new(slug: String, ymd: String, object_name: String, category: Option<String>) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            object_name,
            category,
            rows: Vec::new(),

            times_and_counts,
            eventids_and_counts: HashMap::new(),
            categories_and_counts: HashMap::new(),
            potentialcriticalities_and_counts: HashMap::new(),
            accountnames_and_counts: HashMap::new(),
            accountdomains_and_counts: HashMap::new(),
            sourceworkstations_and_counts: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.eventids_and_counts.clear();
        self.categories_and_counts.clear();
        self.potentialcriticalities_and_counts.clear();
        self.accountnames_and_counts.clear();
        self.accountdomains_and_counts.clear();
        self.sourceworkstations_and_counts.clear();

        self.milliseconds_and_counts.clear();
    }
}

impl Parser for WindowsServerParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}


#[derive(Debug, Clone)]
pub struct VPNServerParser {
    pub slug: String,
    pub ymd: String,
    pub rows: Vec<Vec<String>>,
    // NOTE Vec<Vec<String>> instead of Vec<String>
    //      so that each row can take a variable number of items

    pub times_and_counts: HashMap<String, i32>,
    pub domains_and_counts: HashMap<String, i32>,
    pub usernames_and_counts: HashMap<String, i32>,
    pub ports_and_counts: HashMap<String, i32>,
    pub source_ips_and_counts: HashMap<String, i32>,

    pub username_aggregates: HashMap<String, i32>,

    pub milliseconds_and_counts: HashMap<String, i32>,
}

impl VPNServerParser {
    pub fn new(slug: String, ymd: String) -> Self {
        // initialize times_and_counts with all hour keys set to 0
        let mut times_and_counts = HashMap::new();
        for hour_key in HOUR_KEYS.iter() {
            times_and_counts.insert(hour_key.to_string(), 0);
        }

        Self {
            slug,
            ymd,
            rows: Vec::new(),

            times_and_counts,
            domains_and_counts: HashMap::new(),
            usernames_and_counts: HashMap::new(),
            ports_and_counts: HashMap::new(),
            source_ips_and_counts: HashMap::new(),

            username_aggregates: HashMap::new(),

            milliseconds_and_counts: HashMap::new(),
        }
    }

    pub fn truncate_all(&mut self) {
        self.truncate_rows();

        self.times_and_counts.clear();
        self.domains_and_counts.clear();
        self.usernames_and_counts.clear();
        self.ports_and_counts.clear();
        self.source_ips_and_counts.clear();

        self.username_aggregates.clear();

        self.milliseconds_and_counts.clear();
    }
}

impl Parser for VPNServerParser {
    fn no_of_rows(&self) -> usize {
        self.rows.len()
    }

    fn truncate_rows(&mut self) {
        self.rows.clear();
    }
}
